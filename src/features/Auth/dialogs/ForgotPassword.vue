<script lang="ts" setup>
import {
  FULL_DATE_TIME_24H_FORMAT,
  dateTimeFormat,
  beautifyPhoneNumber,
  timeCountDown,
} from '@utils';
import { useGlobalData, useNow, usePageTracker } from '@composables';
import { useForm } from 'vee-validate';
import { CountryRegion, APIResponseError, ResendOTPPayload } from '@types';
import { TurnstileCaptcha } from '../components';
import { useUserForgotPasswordMutation, useUserResendOTPMutation } from '@services';
import { isEmpty, omitBy } from 'lodash';
import * as yup from 'yup';

const PASSWORD_REGEX = {
  DIGIT: /\d/,
  LOWERCASE: /[a-z]/,
  UPPERCASE: /[A-Z]/,
  SPECIAL: /[`~!@#$%^&*()\-_+=[{\]}\\|;:'",<.>/?]/,
} as const;

const FORM_STEPS = {
  MOBILE_NUMBER: 1,
  OTP: 2,
  PASSWORD: 3,
} as const;

type FormStep = (typeof FORM_STEPS)[keyof typeof FORM_STEPS];

const ERROR_TYPES = {
  INVALID_MOBILE_NUMBER: 'invalid_mobile_number',
  INVALID_VALUE: 'Invalid value',
  INVALID_OTP: 'invalid_otp',
  OTP_LIMITED: 'otp_limited',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

interface FormValues {
  mobile_number: string;
  otp: string;
  password: string;
  cf_password: string;
  expire_at: string;
  next_otp_at: string;
  captcha_token: string;
}

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const mutationForgotPassword = useUserForgotPasswordMutation();
const mutationResendOTP = useUserResendOTPMutation();
const now = useNow();

const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { track } = usePageTracker();
const { isTestingEnv, turnstileDumyToken } = useGlobalData();

const step = ref<FormStep>(FORM_STEPS.MOBILE_NUMBER);
const country = ref<CountryRegion>();
const matched = ref(false);
const captchaRef = ref<InstanceType<typeof TurnstileCaptcha> | null>(null);

function createValidationSchema(currentStep: FormStep) {
  const schemas = {
    [FORM_STEPS.MOBILE_NUMBER]: {
      mobile_number: yup.string().required(t('MOBILE_NUMBER_REQUIRED')),
    },
    [FORM_STEPS.OTP]: {
      otp: yup
        .string()
        .required(t('SIGNUP_FORM_OTP_REQUIRED'))
        .length(6, t('SIGNUP_FORM_OTP_INVALID')),
    },
    [FORM_STEPS.PASSWORD]: {
      password: yup
        .string()
        .required(t('PASSWORD_REQUIRED'))
        .test('password-strength', t('PASSWORD_INVALID'), (value = '') => {
          const isValid = validatePasswordStrength(value);
          matched.value = isValid;
          return isValid;
        }),
      cf_password: yup
        .string()
        .required(t('RE_ENTER_PASSWORD_REQUIRED'))
        .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
    },
  };

  return yup.object().shape(schemas[currentStep]);
}

const validationSchema = computed(() => createValidationSchema(step.value));

function validatePasswordStrength(password: string): boolean {
  const checks = {
    length: password.length >= 8,
    lowercase: PASSWORD_REGEX.LOWERCASE.test(password),
    uppercase: PASSWORD_REGEX.UPPERCASE.test(password),
    digit: PASSWORD_REGEX.DIGIT.test(password),
    special: PASSWORD_REGEX.SPECIAL.test(password),
  };

  return Object.values(checks).every(Boolean);
}

function formatMobileNumber(mobileNumber: string): string {
  return `${country.value?.code}${mobileNumber}`.replace(/\+/g, '');
}

const initialValues: FormValues = {
  mobile_number: '',
  otp: '',
  password: '',
  cf_password: '',
  expire_at: '',
  next_otp_at: '',
  captcha_token: '',
};

const { handleSubmit, values, setFieldError, setFieldValue, setTouched } = useForm({
  initialValues,
  validationSchema,
});

const isResendDisabled = computed(() => +new Date(values.next_otp_at) > now.value);
const submitButtonLabel = computed(() =>
  step.value === FORM_STEPS.MOBILE_NUMBER
    ? t('FORGOTPASSWORD_BUTTON_SENDOTP')
    : t('FORGOTPASSWORD_BUTTON_NEXT'),
);

function handleApiError(err: APIResponseError): void {
  const { data, error_message } = err;

  const errorHandlers = {
    [ERROR_TYPES.INVALID_MOBILE_NUMBER]: () => {
      step.value = FORM_STEPS.OTP;
      setTouched(false);
    },
    [ERROR_TYPES.INVALID_VALUE]: () => {
      step.value = FORM_STEPS.OTP;
      setTouched(false);
    },
    [ERROR_TYPES.INVALID_OTP]: () => {
      const message = data?.attempts === 4 ? t('OTP_LIMIT_4') : t('INVALID_OTP');
      setFieldError('otp', message);
    },
    [ERROR_TYPES.OTP_LIMITED]: () => {
      const field = step.value === FORM_STEPS.MOBILE_NUMBER ? 'mobile_number' : 'otp';
      setFieldError(field, t('REACH_LIMIT_RESEND'));
    },
    [ERROR_TYPES.DEFAULT]: () => setFieldError('otp', t(error_message)),
  };

  const handler = errorHandlers[error_message as ErrorType] || errorHandlers[ERROR_TYPES.DEFAULT];
  handler();
  handleRestCaptcha();
}

const onSubmit = handleSubmit((formValues): void => {
  const payload = omitBy(
    {
      mobile_number: formatMobileNumber(formValues.mobile_number),
      otp: formValues.otp,
      password: formValues.password,
      captcha_token: isTestingEnv.value ? turnstileDumyToken.value : formValues.captcha_token,
    },
    (value) => value === '' || value === undefined || isEmpty(value),
  );

  mutationForgotPassword.mutate(payload, {
    onSuccess: (data) => {
      if (data.next_otp_at && data.expire_at) {
        setFieldValue('expire_at', data.expire_at);
        setFieldValue('next_otp_at', data.next_otp_at);
      }

      setTouched(false);
      trackStepAction('next');

      step.value++;

      if (step.value > FORM_STEPS.PASSWORD) {
        emits('close');
        openDialog('login', { resetPW: true });
      }
    },
    onError: handleApiError,
  });
});

function resendOTP(): void {
  const mobile_number = formatMobileNumber(values.mobile_number);
  const payload: ResendOTPPayload = {
    mobile_number,
    type: 'forgot_password',
    captcha_token: isTestingEnv.value ? turnstileDumyToken.value : values.captcha_token,
  };

  mutationResendOTP.mutate(payload, {
    onSuccess: (data) => {
      if (data.next_otp_at && data.expire_at) {
        setFieldValue('expire_at', data.expire_at);
        setFieldValue('next_otp_at', data.next_otp_at);
      }
      setFieldValue('otp', '');
    },
    onError: handleApiError,
  });
}

function trackStepAction(target: string): void {
  track({
    id: 'forgot_password',
    action: 'click',
    data: {
      target,
      step: Object.keys(FORM_STEPS)[step.value - 1]?.toLowerCase(),
    },
  });
}

function handleBack(): void {
  const stepActions = {
    [FORM_STEPS.OTP]: () => {
      setFieldValue('otp', '');
      step.value = FORM_STEPS.MOBILE_NUMBER;
    },
    [FORM_STEPS.PASSWORD]: () => {
      setFieldValue('password', '');
      setFieldValue('cf_password', '');
      step.value = FORM_STEPS.OTP;
    },
    default: () => {
      emits('close');
      openDialog('login');
    },
  };

  const action = stepActions[step.value as keyof typeof stepActions] || stepActions.default;
  if (step.value !== FORM_STEPS.MOBILE_NUMBER) trackStepAction('back');
  else trackStepAction('close');
  action();
  handleRestCaptcha();
}

function handleClick(e: Event): void {
  const target = e.target as HTMLElement;

  if (target.id === 'resendOTP' && +new Date(values.next_otp_at) <= now.value) {
    if (mutationResendOTP.isPending.value) return;
    track({
      id: 'forgot_password',
      action: 'click',
      data: {
        target: 'resend_otp',
      },
    });
    resendOTP();
  }
}

function handleCaptchaSuccess(token: string): void {
  setFieldValue('captcha_token', token);
}

function handleCaptchaError(): void {
  setFieldValue('captcha_token', '');
}

function handleRestCaptcha(): void {
  if (captchaRef.value) captchaRef.value.reset();
  setFieldValue('captcha_token', '');
}

watch(step, handleRestCaptcha);

onMounted(async () => {
  await nextTick();
  addEventListener('click', handleClick);
});

onBeforeUnmount(() => {
  removeEventListener('click', handleClick);
});
</script>

<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button track-id="disable-track" shape="square" variant="secondary" @click="handleBack">
        <Icon name="arrow-left" :size="14" />
      </Button>
    </template>

    <template #header>
      <div class="text-capitalize" v-html="t('FORGOTPASSWORD_TITLE')" />
    </template>

    <q-form @submit.prevent="onSubmit" class="flex flex-col">
      <section v-show="step === FORM_STEPS.MOBILE_NUMBER">
        <div class="text-sm text-center mb-5" v-html="t('FORGOTPASSWORD_DESC')" />
        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="country = $event"
          autofocus
        />
      </section>

      <section v-show="step === FORM_STEPS.OTP">
        <div class="text-md text-center mb-5">
          <span v-html="t('FORGOTPASSWORD_ACCOUNTEXISTS_1')" /><br />
          <b class="text-xl">
            {{ beautifyPhoneNumber(formatMobileNumber(values.mobile_number)) }}
          </b>
          <br />
          <span v-html="t('FORGOTPASSWORD_ACCOUNTEXISTS_3')" />
        </div>

        <VeeOTP class="mb-5" name="otp" :num-inputs="6" />

        <div v-if="values.expire_at" class="text-xs text-center mb-3">
          {{
            t('OTP_VALID_UNTIL', {
              TIME: dateTimeFormat(values.expire_at, FULL_DATE_TIME_24H_FORMAT),
            })
          }}
        </div>

        <div
          class="text-xs text-center mb-5"
          v-html="
            isResendDisabled
              ? t('SIGNUP_FORM_OTPTEXT_RESEND', {
                  TIME: timeCountDown(+new Date(values.next_otp_at) - now),
                })
              : t('SIGNUP_FORM_OTPTEXT_RESEND_NOCOUNTDOWN')
          "
        />
      </section>

      <section v-show="step === FORM_STEPS.PASSWORD">
        <div class="text-sm text-center mb-5" v-html="t('FORGOTPASSWORD_CREATENEWPASSWORD_1')" />

        <VeeInput class="mb-5" name="password" :label="t('PASSWORD')" type="password" />

        <Requirements
          v-show="values.password && !matched"
          class="mb-3 -mt-4"
          :password="values.password"
          @valid="matched = $event"
        />

        <VeeInput class="mb-5" name="cf_password" :label="t('RE_ENTER_PASSWORD')" type="password" />
      </section>

      <TurnstileCaptcha
        v-if="!isTestingEnv"
        ref="captchaRef"
        class="mb-5"
        @success="handleCaptchaSuccess"
        @error="handleCaptchaError"
      />

      <div class="text-center">
        <Button
          track-id="disable-track"
          :disable="!values.captcha_token && !isTestingEnv"
          type="submit"
          :loading="mutationForgotPassword.isPending.value"
          :label="submitButtonLabel"
        />
      </div>
    </q-form>
  </Dialog>
</template>
