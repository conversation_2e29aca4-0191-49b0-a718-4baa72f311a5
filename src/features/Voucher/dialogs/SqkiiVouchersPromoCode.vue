<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { extractPromoCode, timeCountDown } from '@utils';
import { APISVResponseError, SVUsedPromoCode } from '@types';
import { useNow, usePageTracker } from '@composables';
import { useSVCheckPromoMutation } from '@services';
import * as yup from 'yup';

const MAX_ATTEMPTS = 5;

const ERROR_TYPES = {
  TEMP_LOCKED: 'temp_locked',
  INVALID_CODE: 'invalid_code',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

interface Emits {
  (e: 'used', payload: SVUsedPromoCode): void;
}

interface FormValues {
  code: string;
}

const emits = defineEmits<Emits>();

const now = useNow();
const checkPromoMutation = useSVCheckPromoMutation();

const { track } = usePageTracker();
const { closeDialog, openDialog } = useMicroRoute();
const { t } = useI18n();

const error = ref('');
const lockedUntil = ref('');

const countdown = computed(() => {
  if (!lockedUntil.value) return 0;
  return Math.max(0, +new Date(lockedUntil.value) - now.value);
});

const isLocked = computed(() => countdown.value > 0);

const loading = computed(() => checkPromoMutation.isPending.value);

const validationSchema = yup.object().shape({
  code: yup.string().required(t('CODE_REQUIRED')),
});

const { handleSubmit, values } = useForm<FormValues>({
  initialValues: {
    code: '',
  },
  validationSchema,
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;

  const errorHandlers = {
    [ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('PROMO_MAX_CREDENTIALS');
      lockedUntil.value = data?.locked_until || '';
    },
    [ERROR_TYPES.INVALID_CODE]: () => {
      const remainingAttempts = MAX_ATTEMPTS - (data?.failed || 0);
      error.value = t('PROMO_INVALID_CREDENTIALS_ATTEMPTS', {
        ATTEMPTS: remainingAttempts,
      });
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

function handleScanPromo(): void {
  track({
    id: 'sqkii_vouchers_promo_code',
    action: 'click',
    data: {
      target: 'scan_promo',
    },
  });

  openDialog('sqkii_vouchers_scan_promo', {
    onScan: onSubmit,
  });
}

function handleClose(): void {
  closeDialog('sqkii_vouchers_promo_code');
}

const onSubmit = handleSubmit(async (values: FormValues): Promise<void> => {
  try {
    const code = extractPromoCode(values.code);
    const data = await checkPromoMutation.mutateAsync(code);

    emits('used', {
      promo_code: values.code,
      bonus_crystals: data.crystals,
    });

    track({
      id: 'sqkii_vouchers_promo_code',
      action: 'click',
      data: {
        target: 'promo_applied',
        bonus_crystals: data.crystals,
      },
    });

    closeDialog('sqkii_vouchers_promo_code');
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
});

function clearError(): void {
  error.value = '';
}

watch(() => values.code, clearError);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="handleClose"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div v-html="t('ENTER_TOP_UP_PROMO_HEADER')" />
    </template>

    <q-form @submit.prevent="onSubmit" class="px-5 text-center">
      <VeeInput class="mb-5" name="code" :error="!!error" autofocus>
        <template #prepend>
          <div class="border-r w-8 ml-[6px] h-[calc(100%-18px)]">
            <Icon name="icons/ic_scan" :size="20" style="opacity: 0.7" @click="handleScanPromo" />
          </div>
        </template>
      </VeeInput>

      <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="error" />

      <Button
        track-id="disable-track"
        :loading="loading"
        :disable="isLocked"
        type="submit"
        :label="isLocked ? timeCountDown(countdown) : t('ENTER_TOP_UP_PROMO_BTN_SUBMIT')"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
