<script lang="ts" setup>
import { useFetchQueries, useGlobalData, useNow, usePageTracker } from '@composables';
import { useForm } from 'vee-validate';
import { useUserStore } from '@stores';
import { timeCountDown } from '@utils';
import { useSVPaymentMutation, useSVVerifyPinMutation } from '@services';
import { APISVResponseError, SVPin, SVPaymentResult } from '@types';
import * as yup from 'yup';

interface Props {
  amount: string;
  outlet_id: string;
}

interface FormValues {
  pin_code: string;
  amount: number;
  outlet_id: string;
}

interface PaymentPayload extends FormValues {
  sdk_linking: {
    user_id: string;
  };
  campaign: string;
}

const PIN_LENGTH = 6;

const ERROR_TYPES = {
  TEMP_LOCKED: 'temp_locked',
  INCORRECT_PIN: 'incorrect_pin',
  PAYMENT_REJECTED: 'rejected',
  PAYMENT_SERVER_INTERRUPTED: 'server_interrupted',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

const props = defineProps<Props>();

const storeUser = useUserStore();
const now = useNow();
const verifyPinMutation = useSVVerifyPinMutation();
const paymentMutation = useSVPaymentMutation();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeAllDialogs, openDialog, closeDialog, push } = useMicroRoute();
const { campaignName } = useGlobalData();
const { svUserQuery, svUserBalanceQuery } = useFetchQueries();
const { track } = usePageTracker();

const error = ref<string>('');
const lockedUntil = ref<string>('');

const isLoading = computed(
  () =>
    verifyPinMutation.isPending.value ||
    paymentMutation.isPending.value ||
    svUserQuery.isFetching.value ||
    svUserBalanceQuery.isFetching.value,
);

const lockoutCountdown = computed(() => {
  if (!lockedUntil.value) return 0;
  return Math.max(0, +new Date(lockedUntil.value) - now.value);
});

const isLockedOut = computed(() => lockoutCountdown.value > 0);
const canSubmit = computed(() => !isLockedOut.value && !isLoading.value);

const formattedCountdown = computed(() => {
  return isLockedOut.value ? timeCountDown(lockoutCountdown.value) : '';
});

const submitButtonLabel = computed(() => {
  return isLockedOut.value ? formattedCountdown.value : t('ENTER_PIN_TOPUP_BTN_SUBMIT');
});

const validationSchema = yup.object().shape({
  pin_code: yup
    .string()
    .required(t('PIN_REQUIRED'))
    .length(PIN_LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
});

const { handleSubmit, values } = useForm<FormValues>({
  initialValues: {
    pin_code: '',
    amount: Number(props.amount),
    outlet_id: props.outlet_id,
  },
  validationSchema,
});

async function verifyPinCode(): Promise<SVPin | undefined> {
  try {
    const data = await verifyPinMutation.mutateAsync(values.pin_code);
    return data;
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('PAYMENT_PIN_MAX_CREDENTIALS');
      lockedUntil.value = data?.locked_until || '';
    },
    [ERROR_TYPES.INCORRECT_PIN]: () => {
      error.value = t('PAYMENT_PIN_INVALID_CREDENTIALS_ATTEMPTS');
    },
    [ERROR_TYPES.PAYMENT_REJECTED]: () => {
      closeAllDialogs();
      trackAction('payment_rejected');
      openDialog('sqkii_vouchers_payment_result', {
        status: 'processing',
      });
    },
    [ERROR_TYPES.PAYMENT_SERVER_INTERRUPTED]: () => {
      closeAllDialogs();
      trackAction('payment_server_interrupted');
      openDialog('sqkii_vouchers_payment_result', {
        status: 'processing',
      });
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

function createPaymentPayload(formValues: FormValues): PaymentPayload {
  if (!user.value) {
    throw new Error('User not authenticated');
  }

  return {
    ...formValues,
    sdk_linking: {
      user_id: user.value.id,
    },
    campaign: campaignName.value,
  };
}

async function processPayment(payload: PaymentPayload): Promise<SVPaymentResult> {
  const paymentData = await paymentMutation.mutateAsync(payload);
  await Promise.all([svUserQuery.refetch(), svUserBalanceQuery.refetch()]);
  return paymentData;
}

function navigateToPaymentResult(paymentData: SVPaymentResult): void {
  push(-1);
  closeAllDialogs();
  trackAction('payment_success');
  openDialog('sqkii_vouchers_payment_result', {
    status: 'processing',
    data: paymentData,
  });
}

function trackAction(action: string): void {
  track({
    id: 'sqkii_vouchers_enter_pin_payment',
    action: 'click',
    data: {
      target: action,
    },
  });
}

function clearError(): void {
  error.value = '';
}

const handleFormSubmit = handleSubmit(async (formValues: FormValues): Promise<void> => {
  if (!user.value) return;

  try {
    const pinVerification = await verifyPinCode();

    if (!pinVerification?.verify) return;

    const payload = createPaymentPayload(formValues);

    const paymentData = await processPayment(payload);

    navigateToPaymentResult(paymentData);
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
});

function handleBackNavigation(): void {
  if (isLoading.value) return;
  closeDialog('sqkii_vouchers_enter_pin_payment');
}

function handleForgotPin(): void {
  closeAllDialogs();
  openDialog('sqkii_vouchers_forgot_pin');
}

watch(() => values.pin_code, clearError);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        @click="handleBackNavigation"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('ENTER_PIN_TOPUP_HEADER')"></div>
    </template>
    <q-form class="text-center" @submit="handleFormSubmit">
      <div class="text-sm mb-5" v-html="t('ENTER_PIN_TOPUP_DESC')"></div>
      <VeeOTP
        class="mb-5"
        name="pin_code"
        :num-inputs="PIN_LENGTH"
        :error="!!error"
        input-type="password"
      />
      <div class="card-error mt-2 mb-5 text-center" v-if="!!error" v-html="t(error)"></div>

      <div
        class="underline text-link text-sm mb-5 cursor-pointer hover:opacity-80"
        @click="handleForgotPin"
        v-html="t('ENTER_PIN_TOPUP_DESC_1')"
      />

      <Button
        track-id="disable-track"
        type="submit"
        :label="submitButtonLabel"
        :disable="!canSubmit"
        :loading="isLoading"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
