<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { APISVResponseError } from '@types';
import { useSVAuthSetPasswordMutation } from '@services';
import * as yup from 'yup';
import { usePageTracker } from '@composables';

interface Props {
  recover_token: string;
}

const props = defineProps<Props>();

const setPasswordMutation = useSVAuthSetPasswordMutation();

const { t } = useI18n();
const { closeAllDialogs, openDialog } = useMicroRoute();
const { track } = usePageTracker();

const matched = ref(false);
const error = ref('');

const loading = computed(() => setPasswordMutation.isPending.value);

const validationSchema = yup.object({
  password: yup.string().required(t('PASSWORD_REQUIRED')),
  cf_password: yup
    .string()
    .required(t('RE_ENTER_PASSWORD_REQUIRED'))
    .oneOf([yup.ref('password')], t('PASSWORD_NOT_MATCH')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    password: '',
    cf_password: '',
    recover_token: props.recover_token,
  },
  validationSchema,
});

const onSubmit = handleSubmit(async (values): Promise<void> => {
  try {
    await setPasswordMutation.mutateAsync({
      password: values.password,
      recover_token: values.recover_token,
    });
    closeAllDialogs();
    openDialog('sqkii_vouchers_link_account', {
      fromRecoverPW: true,
    });
    track({
      id: 'sqkii_vouchers_recover_password',
      action: 'click',
      data: {
        target: 'recover_password_success',
      },
    });
  } catch (err) {
    const { message } = err as APISVResponseError;
    error.value = t(message);
  }
});

function onClose(): void {
  if (loading.value) return;
  closeAllDialogs();
}

function clearError(): void {
  error.value = '';
}

watch(() => [values.password, values.cf_password], clearError);
</script>
<template>
  <Dialog @close="onClose">
    <template #header>
      <div v-html="t('KEE_RECOVER_PW_HEADER')"></div>
    </template>
    <q-form @submit.prevent="onSubmit" class="text-center">
      <div class="text-sm px-7 mb-5" v-html="t('KEE_RECOVER_PW_DESC')"></div>
      <VeeInput
        class="mb-5"
        name="password"
        :label="t('PASSWORD')"
        :error="!!error"
        type="password"
      />
      <Requirements
        class="mb-3 -mt-4"
        v-if="values.password && !matched"
        :password="values.password"
        @valid="matched = $event"
      />

      <VeeInput
        class="mb-5"
        name="cf_password"
        :label="t('RE_ENTER_PASSWORD')"
        :error="!!error"
        type="password"
      />

      <div class="card-error mt-2 mb-5 text-center" v-if="!!error" v-html="t(error)"></div>

      <Button
        track-id="disable-track"
        class="!w-[210px]"
        :loading="loading"
        :label="t('KEE_RECOVER_PW_BTN_SUBMIT')"
        type="submit"
      />
    </q-form>
  </Dialog>
</template>
