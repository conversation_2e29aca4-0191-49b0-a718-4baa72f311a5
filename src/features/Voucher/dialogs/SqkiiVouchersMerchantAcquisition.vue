<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { errorNotify, isValidMobileNumber } from '@utils';
import { useSubmitSVReferralMutation } from '@services';
import { APIResponseError, CountryRegion } from '@types';
import { usePageTracker } from '@composables';
import * as yup from 'yup';

const mutation = useSubmitSVReferralMutation();

const { t } = useI18n();
const { openDialog, closeDialog } = useMicroRoute();
const { track } = usePageTracker();

const SINGAPORE_COUNTRY_CONFIG: CountryRegion = {
  name: 'Singapore',
  code: '+65',
  iso: 'SG',
  flag: 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg',
  mask: ['#### ####'],
  currency: 'SGD',
  currencyName: 'Singapore Dollar (SGD)',
  url: 'https://huntthemouse.sqkii.com',
};

const FORM_INITIAL_VALUES = {
  name: '',
  email: '',
  mobile_number: '',
  name_of_business: '',
};

const selectedCountry = ref<CountryRegion>(SINGAPORE_COUNTRY_CONFIG);

const isSubmitting = computed(() => mutation.isPending.value);

const validationSchema = yup.object({
  name: yup.string().trim().required(t('NAME_REQUIRED')),
  email: yup.string().trim().required(t('EMAIL_REQUIRED')).email(t('EMAIL_INVALID')),
  mobile_number: yup
    .string()
    .required(t('MOBILE_NUMBER_REQUIRED'))
    .test('mobile-number', t('MERCHANT_ACQUISITION_INVALIDMOBILENUMBER'), (value) => {
      if (!value) return false;
      const fullNumber = selectedCountry.value.code + value;
      return isValidMobileNumber(fullNumber, 'SG');
    }),
  name_of_business: yup.string().trim().required(t('NAME_OF_BUSINESS_REQUIRED')),
});

const { handleSubmit } = useForm({
  initialValues: FORM_INITIAL_VALUES,
  validationSchema,
});

function formatMobileNumber(mobileNumber: string, countryCode: string): string {
  if (!mobileNumber || !countryCode) {
    throw new Error('Mobile number and country code are required');
  }
  return `${countryCode}${mobileNumber}`.replace(/\+/g, '');
}

function trackSuccessfulSubmission(): void {
  track({
    id: 'sqkii_vouchers_merchant_acquisition',
    action: 'click',
    data: {
      target: 'submitted_success',
    },
  });
}

function handleSuccessfulSubmission(): void {
  trackSuccessfulSubmission();
  closeDialog('sqkii_vouchers_merchant_acquisition');
  openDialog('sqkii_vouchers_merchant_acquisition_success');
}

function handleSubmissionError(error: APIResponseError): void {
  const { error_message } = error;
  errorNotify({
    message: t(error_message),
  });
}

const onSubmit = handleSubmit(async (values): Promise<void> => {
  try {
    const payload = {
      ...values,
      mobile_number: formatMobileNumber(values.mobile_number, selectedCountry.value.code),
    };

    await mutation.mutateAsync(payload);
    handleSuccessfulSubmission();
  } catch (error) {
    handleSubmissionError(error as APIResponseError);
  }
});

function handleCountryUpdate(country: CountryRegion): void {
  selectedCountry.value = country;
}
</script>
<template>
  <Dialog>
    <template #header>
      <div v-html="t('MERCHANT_ACQUISITION_HEADER')"></div>
    </template>

    <q-form @submit.prevent="onSubmit" class="text-center">
      <div class="text-sm mb-5" v-html="t('MERCHANT_ACQUISITION_DESC_1')"></div>

      <div class="px-5 mb-5">
        <VeeInput name="name" :label="t('NAME')" class="mb-5" autofocus />

        <VeeInput name="email" :label="t('EMAIL_ADDRESS')" class="mb-5" />

        <VeeInputCountry
          class="mb-5"
          :label="t('MOBILE_NUMBER')"
          name="mobile_number"
          @update:country="handleCountryUpdate"
          :customCountries="[SINGAPORE_COUNTRY_CONFIG]"
        />

        <VeeInput name="name_of_business" :label="t('NAME_BUSINESS')" class="mb-5" />
      </div>

      <div class="text-sm opacity-70 mb-5" v-html="t('MERCHANT_ACQUISITION_DESC_2')"></div>

      <Button track-id="disable-track" :label="t('SUBMIT')" type="submit" :loading="isSubmitting" />
    </q-form>
  </Dialog>
</template>
