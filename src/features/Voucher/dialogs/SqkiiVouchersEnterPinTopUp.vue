<script lang="ts" setup>
import { useGlobalData, useNow, usePageTracker } from '@composables';
import { useForm } from 'vee-validate';
import { timeCountDown } from '@utils';
import { useUserStore } from '@stores';
import { APISVResponseError } from '@types';
import { useSVTopUpMutation } from '@services';
import * as yup from 'yup';

const PIN_LENGTH = 6;

const ERROR_TYPES = {
  TEMP_LOCKED: 'temp_locked',
  INCORRECT_PIN: 'incorrect_pin',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

interface Props {
  amount: number;
  promo_code?: string;
}

const props = defineProps<Props>();

const mutation = useSVTopUpMutation();
const storeUser = useUserStore();
const now = useNow();
const { track } = usePageTracker();

const { user } = storeToRefs(storeUser);
const { t } = useI18n();
const { closeDialog, closeAllDialogs, openDialog } = useMicroRoute();
const { campaignName } = useGlobalData();

const error = ref('');
const locked_until = ref('');

const loading = computed(() => mutation.isPending.value);

const countdown = computed(() => {
  if (!locked_until.value) return 0;
  return Math.max(0, +new Date(locked_until.value) - now.value);
});

const isLocked = computed(() => countdown.value > 0);

const submitButtonLabel = computed(() => {
  if (isLocked.value) return timeCountDown(countdown.value);
  return t('ENTER_PIN_TOPUP_BTN_SUBMIT');
});

const canSubmit = computed(() => !isLocked.value && !loading.value);

const validationSchema = yup.object().shape({
  pin_code: yup
    .string()
    .required(t('PIN_REQUIRED'))
    .length(PIN_LENGTH, t('SIGNUP_FORM_OTP_INVALID')),
});

const { handleSubmit, values } = useForm({
  initialValues: {
    pin_code: '',
  },
  validationSchema,
});

const onSubmit = handleSubmit(async (values): Promise<void> => {
  try {
    if (!user.value) return;

    const payload = {
      pin_code: values.pin_code,
      amount: props.amount,
      promo_code: props.promo_code,
      sdk_linking: {
        user_id: user.value.id,
      },
      campaign: campaignName.value,
    };

    const data = await mutation.mutateAsync(payload);

    trackTopUpAction('topup_success');
    closeAllDialogs();
    openDialog('sqkii_vouchers_top_up_result', {
      confirm_link: data.confirm_link,
      status: 'processing',
    });
  } catch (err) {
    handleApiError(err as APISVResponseError);
  }
});

function handleApiError(err: APISVResponseError): void {
  const { data, message } = err;
  const errorHandlers = {
    [ERROR_TYPES.TEMP_LOCKED]: () => {
      error.value = t('LINK_KEE_MAX_CREDENTIALS');
      locked_until.value = data?.locked_until || '';
    },
    [ERROR_TYPES.INCORRECT_PIN]: () => {
      error.value = t('PIN_INVALID_CREDENTIALS_ATTEMPTS');
    },
    [ERROR_TYPES.DEFAULT]: () => {
      error.value = t(message);
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];
  handler();
}

function trackTopUpAction(action: string): void {
  track({
    id: 'sqkii_vouchers_enter_pin_topup',
    action: 'click',
    data: {
      target: action,
      amount: props.amount,
      promo_code: props.promo_code,
    },
  });
}

function handleBack(): void {
  if (loading.value) return;
  trackTopUpAction('back');
  closeDialog('sqkii_vouchers_enter_pin_topup');
}

function handleForgotPin(): void {
  trackTopUpAction('forgot_pin');
  closeAllDialogs();
  openDialog('sqkii_vouchers_forgot_pin');
}

function clearError(): void {
  error.value = '';
  locked_until.value = '';
}

watch(() => values.pin_code, clearError);
</script>
<template>
  <Dialog hide-close>
    <template #btnTopLeft>
      <Button
        class="absolute -top-3 -left-3"
        track-id="disable-track"
        shape="square"
        variant="secondary"
        @click="handleBack"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>
    <template #header>
      <div v-html="t('ENTER_PIN_TOPUP_HEADER')"></div>
    </template>
    <q-form class="text-center">
      <div class="text-sm mb-5" v-html="t('ENTER_PIN_TOPUP_DESC')"></div>
      <VeeOTP
        class="mb-5"
        name="pin_code"
        :num-inputs="PIN_LENGTH"
        :error="!!error"
        input-type="password"
      />

      <div v-if="error" class="card-error mt-2 mb-5 text-center" v-html="t(error)"></div>

      <div
        class="underline text-link text-sm mb-5"
        @click="handleForgotPin"
        v-html="t('ENTER_PIN_TOPUP_DESC_1')"
      ></div>

      <Button
        track-id="disable-track"
        :label="submitButtonLabel"
        :disable="!canSubmit"
        :loading="loading"
        @click="onSubmit"
        class="w-full"
      />
    </q-form>
  </Dialog>
</template>
