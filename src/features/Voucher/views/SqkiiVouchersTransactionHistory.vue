<script lang="ts" setup>
import {
  SqkiiVouchersTransactionType<PERSON>abels,
  SqkiiVouchersTransactionStatus,
  SqkiiVouchersTransactionType,
} from '@enums';
import { onClickOutside } from '@vueuse/core';
import { dateTimeFormat, FULL_DATE_TIME_24H_FORMAT, numeralFormat } from '@utils';
import { SVTransactionHistory } from '@types';
import { useSVHistoryQuery } from '@services';
import { usePageTracker } from '@composables';

interface FilterOption {
  readonly label: string;
  readonly value: string;
}

type TabType = 'All' | SqkiiVouchersTransactionStatus | string;

const PAGE_SIZE = 20;

const FILTER_OPTIONS: readonly FilterOption[] = [
  { label: 'All Time', value: 'all' },
  { label: 'Today', value: 'today' },
  { label: 'Last 30 Days', value: '30d' },
  { label: 'Last 60 Days', value: '60d' },
  { label: 'Last 90 Days', value: '90d' },
] as const;

const { t } = useI18n();
const { push, openDialog } = useMicroRoute();
const { track } = usePageTracker();

const transactions = ref<SVTransactionHistory[]>([]);
const totalPage = ref(1);
const page = ref(1);
const target = ref<HTMLElement | null>(null);
const showFilter = ref(false);
const filterBy = ref<string>('all');
const tab = ref<TabType>('All');

const tabLabels = computed((): TabType[] => [
  'All',
  ...Object.values(SqkiiVouchersTransactionTypeLabels),
  ...Object.values(SqkiiVouchersTransactionStatus),
]);

const queryParams = computed(() => ({
  filter: filterBy.value,
  type: getTransactionTypeFromTab(tab.value),
  status: getTransactionStatusFromTab(tab.value),
  page: page.value,
}));

const hasMorePages = computed(() => page.value < totalPage.value);

function getTransactionTypeFromTab(tabValue: TabType): string {
  const typeKey = Object.keys(SqkiiVouchersTransactionTypeLabels).find(
    (key) => SqkiiVouchersTransactionTypeLabels[key as SqkiiVouchersTransactionType] === tabValue,
  );
  return typeKey ?? 'all';
}

function getTransactionStatusFromTab(tabValue: TabType): string | undefined {
  return Object.values(SqkiiVouchersTransactionStatus).includes(
    tabValue as SqkiiVouchersTransactionStatus,
  )
    ? tabValue
    : undefined;
}

function getTransactionId(transaction: SVTransactionHistory): string {
  return transaction.type === SqkiiVouchersTransactionType.PAYMENT
    ? transaction.payment_id
    : transaction.txn_id.toString();
}

function formatAmount(transaction: SVTransactionHistory): string {
  return `${transaction.currency}$ ${numeralFormat(transaction.amount, '0,0.00')}`;
}

function handleFilterSelect(value: string): void {
  filterBy.value = value;
  showFilter.value = false;
  track({
    id: 'sqkii_vouchers_transaction_history',
    action: 'click',
    data: {
      target: 'filter',
      filter_type: value,
    },
  });
  resetPagination();
}

function handleTabSelect(selectedTab: TabType): void {
  tab.value = selectedTab;
  track({
    id: 'sqkii_vouchers_transaction_history',
    action: 'click',
    data: {
      target: 'tab',
      tab_type: selectedTab,
    },
  });
  resetPagination();
}

function handleTransactionClick(transaction: SVTransactionHistory): void {
  openDialog('sqkii_vouchers_transaction_detail', { transaction });
  track({
    id: 'sqkii_vouchers_transaction_history',
    action: 'click',
    data: {
      target: 'transaction_detail',
      transaction_id:
        transaction.type === SqkiiVouchersTransactionType.PAYMENT
          ? transaction.payment_id
          : transaction.txn_id.toString(),
    },
  });
}

function handleLoadMore(_index: number, done: (stop?: boolean) => void): void {
  if (hasMorePages.value) {
    page.value++;
  }
  done();
}

function toggleFilterDropdown(): void {
  showFilter.value = !showFilter.value;
}

function resetPagination(): void {
  page.value = 1;
}

onClickOutside(target, () => {
  showFilter.value = false;
});

useSVHistoryQuery(queryParams, {
  select(response) {
    const { total, data } = response;
    totalPage.value = Math.ceil(total / PAGE_SIZE);
    transactions.value = page.value > 1 ? [...transactions.value, ...data] : data;
    return response;
  },
});
</script>
<template>
  <div class="fullscreen flex flex-col flex-nowrap bg-[#090422] overflow-hidden">
    <div class="relative flex items-center justify-center h-16 px-20 mb-5 shrink-0">
      <Button
        track-id="disable-track"
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="w-full text-lg font-extrabold text-center"
        v-html="t('TRANSACTION_HISTORY_HEADER')"
      ></div>
      <Button
        track-id="disable-track"
        shape="square"
        class="absolute right-4"
        @click="toggleFilterDropdown"
      >
        <Icon name="ic_fillter" :size="14" />
      </Button>
      <div
        ref="target"
        class="flex flex-col items-end justify-center gap-4 overflow-hidden filter-hint"
        :class="{
          'filter-hint-open': showFilter,
        }"
      >
        <div
          v-for="{ value, label } in FILTER_OPTIONS"
          :key="value"
          class="relative text-base cursor-pointer hover:opacity-80"
          @click="handleFilterSelect(value)"
        >
          <span
            class="text-sm"
            :class="{
              'font-bold': filterBy === value,
            }"
          >
            {{ label }}
          </span>
          <Icon
            v-if="filterBy === value"
            name="arrow-left"
            :size="15"
            class="absolute top-1 -right-4"
          />
        </div>
      </div>
    </div>
    <div
      class="flex items-center gap-2 mx-10 mb-5 overflow-x-auto flex-nowrap snap-x snap-mandatory shrink-0"
    >
      <div
        class="capitalize snap-start bg-[#200D37] border border-[#B663E9] rounded-[10px] px-5 py-3 w-max shrink-0"
        :class="{
          '!bg-[#5D3AC0] ': item === tab,
        }"
        v-for="item in tabLabels"
        :key="item"
        v-html="item"
        @click="handleTabSelect(item)"
      ></div>
    </div>

    <q-infinite-scroll
      @load="handleLoadMore"
      :offset="200"
      class="flex flex-col h-full gap-5 px-5 pb-5 overflow-y-auto flex-nowrap"
    >
      <template v-if="transactions.length">
        <div
          v-for="transaction in transactions"
          :key="transaction.id"
          class="relative px-3 py-5 rounded cursor-pointer hover:opacity-90 transition-opacity"
          style="
            background: linear-gradient(
              179deg,
              rgba(37, 25, 109, 0.95) 1.24%,
              rgba(29, 65, 137, 0.9) 46.04%
            );
          "
          @click="handleTransactionClick(transaction)"
        >
          <div class="flex items-center justify-between mb-2">
            <div class="capitalize" :class="transaction.status">
              {{ transaction.status }}
            </div>
            <Icon name="arrow-left" class="!rotate-180" :size="16" />
          </div>

          <div
            class="text-sm"
            v-html="
              t('TRANSACTION_HISTORY_ID', {
                ID: getTransactionId(transaction),
              })
            "
          />

          <div
            class="text-sm"
            v-html="
              t('TRANSACTION_HISTORY_AMOUNT', {
                AMOUNT: formatAmount(transaction),
              })
            "
          />

          <div class="text-sm">{{ transaction.outlet_name }}</div>

          <div class="text-sm opacity-50">
            {{ dateTimeFormat(transaction.updated_at, FULL_DATE_TIME_24H_FORMAT) }}
          </div>
        </div>
      </template>
      <template v-else>
        <div
          class="flex items-center justify-center h-full text-2xl font-bold"
          v-html="t('TRANSACTION_HISTORY_NO_DATA')"
        ></div>
      </template>
      <template v-slot:loading>
        <div class="justify-center row q-my-md">
          <q-spinner-dots color="primary" size="20px" />
        </div>
      </template>
    </q-infinite-scroll>
  </div>
</template>
<style lang="scss" scoped>
.filter-hint {
  position: absolute;
  bottom: 20px;
  right: 20px;
  transform: translateY(calc(100% + 14px));
  width: max-content;
  background: linear-gradient(
    180.05deg,
    rgba(37, 25, 109, 0.9) 15.77%,
    rgba(29, 65, 137, 0.9) 98.5%
  );
  padding: 0;
  border: 1px solid #11d1f9;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
  border-radius: 10px 2px 10px 10px;
  opacity: 0;
  transition: all 0.3s;
  flex-wrap: nowrap;
  z-index: 9999999;
  height: 0px;
  &-open {
    height: 200px;
    transition: all 0.3s;
    opacity: 1;
    padding: 0 30px;
  }
}
.refunded {
  margin-left: -16px;
  background: linear-gradient(90deg, #bc18d7 0%, #dc4bf3 100%);
  width: max-content;
  padding: 2px 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 2px;
}
.failed {
  margin-left: -16px;
  background: linear-gradient(90deg, #f64343 -0.34%, #ff4d4d 99.59%);
  width: max-content;
  padding: 2px 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 2px;
}
.completed {
  margin-left: -16px;
  background: linear-gradient(90deg, #30a256 -0.34%, #3ee75a 99.59%);
  width: max-content;
  padding: 2px 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 2px;
}
</style>
