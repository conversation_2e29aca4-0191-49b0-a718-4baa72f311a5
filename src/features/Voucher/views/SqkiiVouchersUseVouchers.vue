<script lang="ts" setup>
import { useMediaDevices, usePageTracker } from '@composables';
import { errorNotify } from '@utils';
import { last, throttle } from 'lodash';
import { APISVResponseError } from '@types';
import { STORAGE_KEYS } from '@enums';
import { useSVPaymentScanMutation } from '@services';

const ERROR_TYPES = {
  OUTLET_INVALID: 'outlet_invalid',
  DEFAULT: 'default',
} as const;

type ErrorType = (typeof ERROR_TYPES)[keyof typeof ERROR_TYPES];

const mutation = useSVPaymentScanMutation();

const { t } = useI18n();
const { push, openDialog, closeDialog } = useMicroRoute();
const { track } = usePageTracker();

const streamVideo = ref<MediaStream | null>(null);

const hasEnabledCamera = computed(() => !!LocalStorage.getItem(STORAGE_KEYS.CAMERA_PERMISSION));

function trackAction(target: string): void {
  track({
    id: 'sqkii_vouchers_use_vouchers',
    action: 'click',
    data: {
      target,
    },
  });
}

function requestCameraSuccess(stream: MediaStream): void {
  streamVideo.value = stream;
  closeDialog('camera_permission');
  trackAction('camera_allowed');
}

function requestCameraFailed(): void {
  errorNotify({
    message: t('CAMERA_SCAN_PAYMENT_NOT_DETECTED'),
    timeout: 8000,
  });
  closeDialog('camera_permission');
  trackAction('camera_blocked');
}

const { request } = useMediaDevices(
  {
    video: {
      facingMode: 'environment',
    },
    audio: false,
  },
  requestCameraSuccess,
  requestCameraFailed,
);

const throttleDecode = throttle(onDecode, 1000);

async function onDecode(code: string): Promise<void> {
  try {
    const c = last(code.split('/'));
    if (!c) return;
    const data = await mutation.mutateAsync(c);
    openDialog('sqkii_vouchers_pay_to_merchant', {
      outlet: data,
    });
    trackAction('scan_payment_success');
  } catch (error) {
    handleApiError(error as APISVResponseError);
  }
}

function handleApiError(error: APISVResponseError): void {
  const { data, message } = error;

  const errorHandlers = {
    [ERROR_TYPES.OUTLET_INVALID]: () => {
      errorNotify({
        message: t('OUTLET_INVALID_CODE'),
      });
    },
    [ERROR_TYPES.DEFAULT]: () => {
      errorNotify({
        message: t(message),
      });
    },
  };

  const errorType = data?.info as ErrorType;
  const handler = errorHandlers[errorType] || errorHandlers[ERROR_TYPES.DEFAULT];

  handler();
}

function handleEnterPaymentCode(): void {
  openDialog('sqkii_vouchers_enter_payment');
  trackAction('enter_payment_code');
}

function handleSearchOutlets(): void {
  push('sqkii_vouchers_search_outlets');
  trackAction('search_outlets');
}

onMounted(async () => {
  await nextTick();
  if (hasEnabledCamera.value) await request();
  else {
    openDialog('camera_permission', {
      onRequest: request,
      description: t('SQKII_VOUCHERS_CAMERA_PERMISSION_DESC'),
    });
  }
});
</script>
<template>
  <div class="fullscreen flex flex-col flex-nowrap bg-[#46425B] pb-5">
    <div class="flex justify-center items-center h-16 px-20 relative shrink-0 mb-5">
      <Button
        track-id="disable-track"
        variant="secondary"
        shape="square"
        class="absolute left-4"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div
        class="text-lg font-extrabold text-center w-full"
        v-html="t('USE_SQKII_VOUCHERS_HEADER')"
      ></div>
      <Button
        track-id="disable-track"
        shape="square"
        class="absolute right-4"
        @click="handleSearchOutlets"
      >
        <Icon name="scan" :size="20" />
      </Button>
    </div>
    <div class="flex flex-col flex-nowrap justify-around h-full">
      <div class="text-center">
        <div class="text-sm mb-5" v-html="t('USE_SQKII_VOUCHERS_DESC_1')"></div>
        <div class="size-[297px] bg-[#555] rounded-lg mx-auto relative mb-10">
          <StreamBarcodeReader
            :stream="streamVideo"
            @decode="throttleDecode"
            class="!w-full !h-full rounded-lg"
          />
        </div>
      </div>
      <div class="text-center">
        <div class="text-sm mb-5 px-10" v-html="t('USE_SQKII_VOUCHERS_DESC_2')"></div>
        <Button
          track-id="disable-track"
          class="!w-[210px]"
          :label="t('USE_SQKII_VOUCHERS_BTN_ID')"
          variant="purple"
          @click="handleEnterPaymentCode"
        />
      </div>
    </div>
  </div>
</template>
