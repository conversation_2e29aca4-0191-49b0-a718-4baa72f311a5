<script setup lang="ts">
import { usePageTracker } from '@composables';

interface Emits {
  (event: 'close'): void;
}

const emits = defineEmits<Emits>();

const { t } = useI18n();
const { push } = useMicroRoute();
const { track } = usePageTracker();

function handleClose(): void {
  emits('close');
}

function goToFAQ(): void {
  track({
    id: 'tips_trick',
    action: 'click',
    data: {
      target: 'faq',
    },
  });
  handleClose();
  push('faq');
}
</script>
<template>
  <Dialog @close="handleClose">
    <template #header>
      <div v-html="t('TIPSANDTRICKS_POPUP_HEADING')"></div>
    </template>

    <div class="text-base font-bold text-center mb-2">
      {{ t('TIPSANDTRICKS_POPUP_DESCRIPTION_2') }}
    </div>
    <div class="text-sm text-center mb-10">
      {{ t('TIPSANDTRICKS_POPUP_DESCRIPTION_1') }}
    </div>

    <div class="relative">
      <PixiAnims
        :width="70"
        :height="70"
        name="timii-waving-2"
        json="timii-waving-2.json"
        size="contain"
        :animation-speed="0.25"
        class="absolute top-[-70px] left-[-15px]"
      />
      <Expansion
        group="tiptricks"
        class="bg-[#091a3c] rounded mb-3"
        v-tracker="{
          id: 'tips_trick',
          data: {
            target: 'toggle',
            id: 1,
          },
        }"
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div class="text-xs opacity-60 mb-1">
              {{ t('TIPSANDTRICKS_POPUP_1_CATEGORY') }}
            </div>
            <div class="text-base font-medium">
              {{ t('TIPSANDTRICKS_POPUP_1_TITLE') }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <q-card-section>
            <div>
              <Icon class="gif-wrapper mb-3" name="gif/silver_capture_new" type="gif" />
            </div>
            <div class="text-sm text-center">
              {{ t('TIPSANDTRICKS_POPUP_1_DESC') }}
            </div>
          </q-card-section>
        </q-card>
      </Expansion>

      <Expansion
        group="tiptricks"
        class="bg-[#091a3c] rounded mb-3"
        v-tracker="{
          id: 'tips_trick',
          data: {
            target: 'toggle',
            id: 2,
          },
        }"
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div class="text-xs opacity-60 mb-1">
              {{ t('TIPSANDTRICKS_POPUP_2_CATEGORY') }}
            </div>
            <div class="text-base font-medium">
              {{ t('TIPSANDTRICKS_POPUP_2_TITLE') }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <q-card-section class="text-center">
            <a
              v-tracker="{
                id: 'tips_trick',
                action: 'click',
                data: {
                  target: 'telegram',
                  url: 'https://t.me/SqkiiSG',
                },
              }"
              href="https://t.me/SqkiiSG"
              target="_blank"
              rel="noopener noreferrer"
            >
              <Button track-id="disable-track" :label="t('TIPSANDTRICKS_POPUP_2_BUTTON')" />
            </a>
          </q-card-section>
        </q-card>
      </Expansion>

      <Expansion
        group="tiptricks"
        class="bg-[#091a3c] rounded"
        v-tracker="{
          id: 'tips_trick',
          data: {
            target: 'toggle',
            id: 3,
          },
        }"
      >
        <template v-slot:header>
          <div class="column flex-nowrap flex-1">
            <div class="text-xs opacity-60 mb-1">
              {{ t('TIPSANDTRICKS_POPUP_3_CATEGORY') }}
            </div>
            <div class="text-base font-medium">
              {{ t('TIPSANDTRICKS_POPUP_3_TITLE') }}
            </div>
          </div>
        </template>
        <q-card style="background: transparent">
          <q-card-section class="text-center">
            <Button
              track-id="disable-track"
              :label="t('TIPSANDTRICKS_POPUP_3_BUTTON')"
              @click="goToFAQ"
            />
          </q-card-section>
        </q-card>
      </Expansion>
    </div>
  </Dialog>
</template>

<style scoped lang="scss">
.gif-wrapper {
  position: relative;
  border-radius: 12px;
  background-color: #000;
  width: 100%;
  overflow: hidden;
  border: 1px solid rgba($color: #fff, $alpha: 0.25);
}
</style>
