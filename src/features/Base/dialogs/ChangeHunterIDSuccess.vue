<script setup lang="ts">
import gsap, { Linear } from 'gsap';
import { usePageTracker } from '@composables';

interface Emits {
  (e: 'close'): void;
}

interface Props {
  hunter_id: string;
  next_fee: number;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { openDialog, closeAllDialogs } = useMicroRoute();
const { track } = usePageTracker();

const animations = ref<gsap.core.Timeline[]>([]);
const autoCloseTimeout = ref<NodeJS.Timeout | null>(null);

function handleReChange(): void {
  track({
    id: 'change_hunter_id_success',
    action: 'click',
    data: {
      hunter_id: props.hunter_id,
      fee: props.next_fee,
      target: 'change_again',
    },
  });
  emits('close');
  openDialog('change_hunter_id');
}

function createFlareAnimation(): gsap.core.Timeline {
  const tl = gsap.timeline();

  tl.fromTo(
    '.upload_flare',
    { scale: 0 },
    {
      scale: 1,
      duration: 1,
      delay: 0.5,
    },
  ).fromTo(
    '.upload_flare',
    { rotate: 0 },
    {
      rotate: 720,
      duration: 20,
      repeat: -1,
      ease: Linear.easeNone,
    },
    '-=0.5',
  );

  return tl;
}

function createTextAnimation(): gsap.core.Tween {
  return gsap.fromTo(
    '.text',
    { scale: 0 },
    {
      scale: 1,
      duration: 0.5,
      stagger: 0.2,
    },
  );
}

function createStarAnimation(): gsap.core.Tween {
  return gsap.fromTo(
    '.upload_star',
    { opacity: 0 },
    {
      opacity: 1,
      yoyo: true,
      repeat: -1,
      delay: 1,
      duration: 1,
    },
  );
}

function setupAutoClose(): void {
  autoCloseTimeout.value = setTimeout(() => {
    cleanup();
    closeAllDialogs();
  }, 8000);
}

function startAnimation(): void {
  const flareTimeline = createFlareAnimation();
  animations.value.push(flareTimeline);

  createTextAnimation();
  createStarAnimation();

  setupAutoClose();
}

function cleanup(): void {
  animations.value.forEach((tl) => {
    tl.kill();
  });
  animations.value.length = 0;

  gsap.killTweensOf('.upload_star');
  gsap.killTweensOf('.text');

  if (autoCloseTimeout.value) {
    clearTimeout(autoCloseTimeout.value);
    autoCloseTimeout.value = null;
  }
}

onMounted(async () => {
  await nextTick();
  startAnimation();
});

onBeforeUnmount(() => {
  cleanup();
});
</script>

<template>
  <div
    class="fit bg-[#090422] flex flex-nowrap flex-col justify-between items-center py-10 relative !overflow-x-hidden"
  >
    <HeaderCrystal class="absolute top-0 right-2" />

    <div class="font-bold text-lg" v-html="t('CHANGEHUNTERID_HEADING')" />

    <div
      class="absolute top-1/2 -translate-y-1/2 w-full flex justify-center items-center"
      :style="{ height: '100vw' }"
    >
      <Icon
        class="upload_flare absolute top-0 left-0 pointer-events-none"
        style="width: 100vw"
        name="upload_flare"
      />
      <Icon
        class="upload_star full-width absolute pointer-events-none top-0 left-0 z-10"
        name="star_frame"
      />
    </div>

    <div class="px-5 text-center">
      <div class="text-sm mb-2 text" v-html="t('CHANGEHUNTERID_NEWHUNTERID_1')" />
      <div class="text-2xl font-bold mb-1 text" v-html="t('CHANGEHUNTERID_NEWHUNTERID_2')" />
      <div class="flex flex-nowrap items-center gap-3 text">
        <div class="font-bold whitespace-nowrap text-6xl">
          {{ hunter_id }}
        </div>
        <Icon
          v-tracker="{
            id: 'change_hunter_id_success',
            action: 'click',
            data: {
              target: 'question_mark',
            },
          }"
          name="question-mark"
          :size="20"
          @click="openDialog('dont_like_hunter_id')"
        />
      </div>
    </div>

    <div class="flex flex-col items-center gap-3">
      <div class="text-sm" v-html="t('CHANGEHUNTERID_NEWHUNTERID_4')" />
      <Button
        track-id="disable-track"
        :title="t('CHANGEHUNTERID_BUTTON_CHANGEAGAIN')"
        :amount="next_fee"
        @click="handleReChange"
      />
    </div>
  </div>
</template>
