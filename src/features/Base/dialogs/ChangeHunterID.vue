<script setup lang="ts">
import { useFetchQueries, usePageTracker } from '@composables';
import { useChangeHunterIdMutation } from '@services';
import { useUserStore } from '@stores';

interface Emits {
  (e: 'close'): void;
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const mutation = useChangeHunterIdMutation();

const { user, gameSettings, crystals } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog } = useMicroRoute();
const { userQuery, gameSettingQuery } = useFetchQueries();
const { track } = usePageTracker();

const loading = computed<boolean>(() => mutation.isPending.value);

const feeChangeId = computed<number>(() => {
  if (!user.value?.change_hunter_id_attempts || !gameSettings.value?.change_hunt_id_fee) {
    return 0;
  }

  const userAttempts = Number(user.value.change_hunter_id_attempts);
  const feeStructure = gameSettings.value.change_hunt_id_fee;

  const applicableFee = feeStructure
    .filter((fee) => Number(fee?.attempt) <= userAttempts)
    .reduce(
      (highest, current) =>
        Number(current?.attempt) > Number(highest?.attempt) ? current : highest,
      feeStructure[0],
    );

  return applicableFee?.amount ?? 0;
});

async function handleChangeHunter(): Promise<void> {
  try {
    await mutation.mutateAsync(undefined);
    await Promise.all([userQuery.refetch(), gameSettingQuery.refetch()]);
    track({
      id: 'change_hunter_id',
      action: 'click',
      data: {
        hunter_id: user.value?.hunter_id,
        fee: feeChangeId.value,
      },
    });
    openDialog('change_hunter_id_success', {
      hunter_id: user.value?.hunter_id,
      next_fee: feeChangeId.value,
    });
    emits('close');
  } catch (error) {
    console.error('Error changing Hunter ID:', error);
  }
}
</script>

<template>
  <Dialog class="text-center" hide-close crystals>
    <template #btnTopLeft>
      <Button
        track-id="disable-track"
        class="absolute -top-3 -left-3"
        shape="square"
        variant="secondary"
        :disabled="loading"
        @click="emits('close')"
      >
        <Icon name="arrow-left" />
      </Button>
    </template>

    <template #header>
      <div v-html="t('CHANGEHUNTERID_POPUP_HEADING')"></div>
    </template>

    <div class="mb-2 text-sm" v-html="t('CHANGEHUNTERID_POPUP_TEXT_1')"></div>
    <div
      class="mb-2 text-lg font-bold"
      v-html="
        t('CHANGEHUNTERID_POPUP_TEXT_SUGGESTION_1', {
          HUNTER_ID: user?.hunter_id,
        })
      "
    ></div>
    <div class="mb-5" v-html="t('CHANGEHUNTERID_POPUP_TEXT_2')"></div>

    <Button
      track-id="disable-track"
      class="mx-auto"
      :title="t('CHANGEHUNTERID_POPUP_BUTTON_CONFIRMCHANGE')"
      :amount="feeChangeId"
      :disable="crystals < feeChangeId"
      :loading="loading"
      @click="handleChangeHunter"
    />
  </Dialog>
</template>
