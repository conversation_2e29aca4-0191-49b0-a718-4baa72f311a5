<script lang="ts" setup>
import { useUserStore } from '@stores';
import { TimelineLogs } from '@types';

interface Emits {
  (e: 'close'): void;
}

interface AdventureLogs {
  ongoing: TimelineLogs[];
  past: TimelineLogs[];
}

const emits = defineEmits<Emits>();

const storeUser = useUserStore();

const { timelineLogs, currentSeason } = storeToRefs(storeUser);
const { openDialog } = useMicroRoute();
const { t } = useI18n();

const adventureLogs = computed<AdventureLogs>(() => {
  const logs = timelineLogs.value;
  return {
    ongoing: logs.filter((t) => t.status === 'ongoing').reverse(),
    past: logs.filter((t) => t.status === 'ended').reverse(),
  };
});

function openTimelineDialog(tl?: TimelineLogs): void {
  if (tl) {
    const index = timelineLogs.value.findIndex((t) => t._id === tl._id);
    openDialog('timeline_logs', { defaultInitialSlide: index });
  } else openDialog('timeline_logs');
  emits('close');
}
</script>

<template>
  <Dialog @close="emits('close')">
    <template #header>
      <div v-html="t('ADVENTURELOG_POPUP_HEADING')"></div>
    </template>
    <div class="relative">
      <div
        class="text-center text-base font-bold mb-1"
        v-html="t('ADVENTURELOG_POPUP_DESCRIPTION_1')"
      ></div>
      <div class="text-center text-sm" v-html="t('ADVENTURELOG_POPUP_DESCRIPTION_2')"></div>
      <template v-if="adventureLogs.ongoing.length">
        <div class="mt-10 opacity-80 ml-2 text-sm" v-html="t('ADVENTURELOG_POPUP_CURRENT')"></div>
        <div class="flex flex-col gap-12">
          <div
            class="card-log current mt-2"
            v-for="tl in adventureLogs.ongoing"
            :key="tl._id"
            v-tracker="{
              id: 'adventure_logs',
              data: {
                adventure_log_id: tl._id,
                adventure_log_city: tl.city,
              },
            }"
            @click="openTimelineDialog(tl)"
          >
            <Icon class="sqkii-smile pointer-events-none" name="sqkii-smile" />
            <Icon class="absolute top-3 right-3" name="current-location" />

            <div class="text-sm" v-html="t(tl.adventure_log_date)"></div>
            <div class="text-sm font-bold" v-html="t(tl.adventure_log_title)"></div>
            <div class="text-sm" v-html="t(tl.adventure_log_content)"></div>
          </div>
        </div>
      </template>

      <div class="mt-3 opacity-80 ml-3 text-sm" v-html="t('ADVENTURELOG_POPUP_COMPLETED')"></div>
      <div
        class="card-log mt-2"
        v-for="(tl, index) in adventureLogs.past"
        :key="tl._id"
        v-tracker="{
          id: 'adventure_logs',
          data: {
            adventure_log_id: tl._id,
            adventure_log_city: tl.city,
          },
        }"
        @click="openTimelineDialog(tl)"
      >
        <Icon
          v-if="index === 0 && currentSeason?.status === 'ended'"
          name="sqkii-smile"
          class="absolute w-[50px] right-0 -top-[45px] pointer-events-none"
        />
        <Icon class="absolute top-3 right-3" name="location" />
        <div class="text-sm" v-html="t(tl.adventure_log_date)"></div>
        <div class="text-sm font-bold" v-html="t(tl.adventure_log_title)"></div>
        <div class="text-sm" v-html="t(tl.adventure_log_content)"></div>
      </div>
      <div
        class="underline text-link mt-3 text-center"
        v-html="t('ADVENTURELOG_POPUP_MOREHUNTS')"
        v-tracker="{
          id: 'adventure_logs',
          data: {
            target: 'more_hunts',
          },
        }"
        @click="openTimelineDialog()"
      ></div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.card-log {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 12px;
  border-radius: 4px;
  background: #091a3c;
  &.current {
    background: linear-gradient(180deg, rgba(147, 75, 218, 0.8) 0%, #511d85 100%);
  }

  & .sqkii-smile {
    position: absolute;
    right: 0px;
    top: -45px;
    width: 50px;
    height: 45px;
  }
}
</style>
