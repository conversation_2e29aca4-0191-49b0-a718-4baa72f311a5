<script lang="ts" setup>
import { useMapStore, useUserStore } from '@stores';
import { delay, getSocials } from '@utils';
import { useWindowSize } from '@vueuse/core';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Navigation } from 'swiper/modules';
import { useFlyTo } from 'vue3-maplibre-gl';
import { TimelineLogs } from '@types';
import { usePageTracker } from '@composables';
import { Swiper as SwiperType } from 'swiper/types';
import 'swiper/css';
import 'swiper/css/navigation';

interface Props {
  defaultInitialSlide?: number;
}

const props = defineProps<Props>();

const storeUser = useUserStore();
const storeMap = useMapStore();

const { timelineLogs, initialSlide } = storeToRefs(storeUser);
const { closeDialog, openDialog, closeAllDialogs, push } = useMicroRoute();
const { width } = useWindowSize();
const { t } = useI18n();
const { flyTo } = useFlyTo({
  map: storeMap.mapInstance,
});
const { tracker } = usePageTracker();

const modules = [Navigation];

const currentSlide = ref(0);

const computedInitialSlide = computed<number>(() => {
  return Number(props.defaultInitialSlide) > -1
    ? Number(props.defaultInitialSlide)
    : (initialSlide.value ?? 0);
});

const slidesPerView = computed<number>(() => Math.floor(width.value / 215));

function shouldEnableLoop(videoLinks: string[]): boolean {
  const slides = slidesPerView.value;
  return videoLinks.length >= slides * 2 && videoLinks.length >= 3;
}

function getVideoInitialSlide(videoLinks: string[]): number {
  return shouldEnableLoop(videoLinks) ? 1 : 0;
}

function getBackground(tl: TimelineLogs): string {
  return `url(${tl.background_image || 'imgs/bg_sg.png'}), linear-gradient(#192565, #090422)`;
}

function handleSlideChange(event: SwiperType): void {
  currentSlide.value = event.activeIndex;
}

async function goToHunt(tl: TimelineLogs): Promise<void> {
  tracker({
    id: 'timeline_logs',
    action: 'click',
    data: {
      target: 'view_hunt',
      locations: tl.adventure_log_location,
      city: tl.city,
      status: tl.status,
    },
  });
  closeAllDialogs();
  push('/home');
  await delay(300);
  await flyTo({
    center: [tl.adventure_log_location.lng, tl.adventure_log_location.lat],
    zoom: 10,
  });
}

function registerSeason(tl: TimelineLogs): void {
  tracker({
    id: 'timeline_logs',
    action: 'click',
    data: {
      target: 'register_interest',
      locations: tl.adventure_log_location,
      city: tl.city,
    },
  });

  openDialog('timeline_register', {
    timeline: tl,
  });
}
</script>

<template>
  <div class="relative fit">
    <Button
      track-id="disable-track"
      class="absolute z-10 top-3 left-2"
      shape="square"
      variant="secondary"
      @click="closeDialog('timeline_logs')"
    >
      <Icon name="arrow-left" />
    </Button>
    <Swiper
      class="fit wrapper-timeline"
      :initial-slide="computedInitialSlide"
      :modules="modules"
      :allow-touch-move="false"
      navigation
      @slide-change="handleSlideChange"
    >
      <SwiperSlide
        class="timeline !bg-cover fit !bg-no-repeat overflow-y-auto"
        v-for="(tl, index) in timelineLogs"
        :key="tl._id"
        :style="{ background: getBackground(tl), backgroundPosition: 'top center' }"
      >
        <div class="backdrop"></div>
        <div
          class="relative pb-5 overflow-y-auto fit"
          :class="{
            'pt-20 !h-[calc(100%-100px)]': !!tl.video_links?.length,
            '!h-[calc(100%-20px)] flex flex-col items-center justify-center':
              !tl.video_links?.length,
          }"
        >
          <template v-if="tl.video_links?.length">
            <Swiper
              :initial-slide="getVideoInitialSlide(tl.video_links)"
              :slides-per-view="slidesPerView"
              :space-between="20"
              centered-slides
              :loop="shouldEnableLoop(tl.video_links)"
              class="video-slide flex justify-center items-center mb-4"
              slide-to-clicked-slide
              v-if="currentSlide === index"
            >
              <SwiperSlide
                v-for="v in tl.video_links"
                :key="v"
                v-slot="{ isActive }"
                class="flex items-center"
              >
                <div class="video m-1.5">
                  <PlyrVideo
                    v-tracker="{
                      id: 'timeline_logs',
                      data: {
                        target: 'play_video',
                        video: v,
                      },
                    }"
                    :source="v"
                    :class="{
                      '!h-[300px]': isActive,
                      '!h-[265px]': !isActive,
                    }"
                  />
                </div>
              </SwiperSlide>
            </Swiper>
          </template>
          <div
            class="mb-5"
            :class="{
              'pt-[50vw]': !tl.video_links?.length,
            }"
          >
            <div class="flex items-center justify-center gap-1 px-5 text-xl text-center">
              <div class="font-extrabold" v-html="t(tl.copy_position_1)"></div>
              -
              <div v-html="t(tl.copy_position_2)"></div>
            </div>
            <div
              class="mb-3 text-lg font-bold text-center text-yellow"
              v-html="t(tl.copy_position_3)"
            ></div>
            <div
              class="px-6 mb-5 text-center"
              :class="{
                '!px-10': !tl.video_links?.length,
              }"
              v-html="t(tl.copy_position_4)"
            ></div>
            <div v-if="tl.status === 'ended'" class="mb-5 text-center">
              <Button
                track-id="disable-track"
                :label="t('BUTTON_VIEW_HUNT')"
                @click="goToHunt(tl)"
              />
            </div>
            <div class="mb-5 text-center" v-if="tl.status === 'ongoing'">
              <Button
                track-id="disable-track"
                :label="t('BUTTON_MORE_HUNT')"
                @click="goToHunt(tl)"
              />
            </div>
            <template v-if="tl.status === 'future'">
              <div class="text-center">
                <div class="px-5 mb-5 italic" v-html="t('TIMELINE_COMINGSOON_SUBTEXT')"></div>
                <Button
                  track-id="disable-track"
                  :disable="!!tl.registered_at"
                  :label="
                    !tl.registered_at
                      ? t('TIMELINE_REGISTERINTEREST_BUTTON')
                      : t('TIMELINE_REGISTERED_BUTTON')
                  "
                  @click="registerSeason(tl)"
                />
              </div>
            </template>
            <template v-else>
              <div class="text-center">
                <div class="mb-5 text-sm" v-html="t('MENU_CTA')"></div>
                <div class="flex items-center justify-center gap-4">
                  <a
                    :href="link"
                    target="_blank"
                    rel="noopener noreferrer"
                    v-for="{ link, icon } in getSocials()"
                    :key="icon"
                  >
                    <Icon :name="icon" :size="25" />
                  </a>
                </div>
              </div>
            </template>
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
  </div>
</template>
<style lang="scss">
.wrapper-timeline {
  .swiper-wrapper {
    align-items: center !important;
  }
  .backdrop {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: #00000080;
    z-index: -1;
    pointer-events: none;
  }
  .timeline {
    .video {
      width: 100%;
      border-radius: 10px;
      box-shadow: 0 0 5px 5px rgb(255 255 255 / 16%);
      background-color: #000000;
    }
    .plyr {
      width: calc(100% - 30px);
      height: 100%;
      min-width: 100px;
      border-radius: 10px;
      &__controls {
        padding: 0;
      }
      &__control--overlaid {
        width: 40px !important;
        height: 40px;
        background: transparent !important;
        border: 4px white solid;
        svg {
          width: 16px;
          height: 16px;
          left: calc(50% - (16px / 2));
          top: calc(50% - (16px / 2));
        }
      }
    }

    .plyr__poster {
      filter: blur(1px);
    }
    .plyr__video-embed,
    .plyr__video-wrapper--fixed-ratio {
      aspect-ratio: 9/16 !important;
    }
    .plyr__controls__item {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .plyr__controls {
      width: 100%;
      height: 100%;
    }
  }

  .swiper-button-prev,
  .swiper-button-next {
    z-index: 99;
  }

  .swiper-button-prev:after,
  .swiper-button-next::after {
    font-size: 24px !important;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.5) !important;
  }
}
</style>
