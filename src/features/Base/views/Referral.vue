<script setup lang="ts">
import { successNotify } from '@utils';
import { useUserStore } from '@stores';
import { copyToClipboard } from 'quasar';
import { useFetchQueries, useGlobalData, usePageTracker } from '@composables';
import { useReferralClaimMutation, useReferralQuery } from '@services';
import { CarAnimation } from '@components';
import { UserReferral } from '@types';

interface SocialNetwork {
  network: string;
  icon: string;
  sharelink?: string;
  link: string;
}

const SKELETON_ITEMS = Array.from({ length: 5 }, (_, i) => i);

const storeUser = useUserStore();
const referralQuery = useReferralQuery();
const mutation = useReferralClaimMutation();

const { user, gameSettings } = storeToRefs(storeUser);
const { t } = useI18n();
const { openDialog, push } = useMicroRoute();
const { isUserLogged, seasonCode } = useGlobalData();
const { userQuery } = useFetchQueries();
const { track } = usePageTracker();

const referrals = computed<UserReferral[]>(() => referralQuery.data.value || []);
const isFetching = computed(() => referralQuery.isLoading.value);
const isClaimLoading = computed(() => mutation.isPending.value);
const baseUrl = computed(() => process.env.APP_END_POINT?.replace(/^https?:\/\//, '') || '');
const referralLink = computed(() => `${baseUrl.value}?ref=${user.value?.referral_code}`);
const displayUrl = computed(
  () => `${baseUrl.value.replace('staging.', '')}/${user.value?.referral_code}`,
);

const socialNetworks = computed<SocialNetwork[]>(() => [
  {
    network: 'facebook',
    icon: 'fb',
    sharelink: `https://www.facebook.com/sharer/sharer.php?u=${referralLink.value}`,
    link: 'https://www.facebook.com/sqkii',
  },
  {
    network: 'instagram',
    icon: 'insta',
    link: 'https://www.instagram.com/sqkiimouse',
  },
  {
    network: 'Telegram',
    icon: 'telegram_w',
    sharelink: `https://t.me/share/url?url=${referralLink.value}&text=${t('REFERRAL_DESC_COPIED', {
      URL: referralLink.value,
    })}`,
    link: `https://t.me/share/url?url=${referralLink.value}`,
  },
  {
    network: 'whatsapp',
    icon: 'wa',
    link: '',
    sharelink: `https://api.whatsapp.com/send?text=${t('REFERRAL_DESC_COPIED', {
      URL: referralLink.value,
    })}`,
  },
]);

async function handleCopyReferralLink(): Promise<void> {
  try {
    const text = t('REFERRAL_DESC_COPIED', { URL: referralLink.value });
    await copyToClipboard(text);
    track({
      id: 'referral',
      action: 'click',
      data: {
        target: 'copy_referral_link',
      },
    });
    successNotify({
      message: t('REFERRAL_COPIED'),
    });
  } catch (error) {
    console.error('Failed to copy referral link:', error);
  }
}

async function handleClaimReferral(id: string): Promise<void> {
  try {
    await mutation.mutateAsync(id);
    await Promise.all([referralQuery.refetch(), userQuery.refetch()]);
    track({
      id: 'referral',
      action: 'click',
      data: {
        target: 'claim_referral',
        referral_id: id,
        hunter_id: user.value?.hunter_id,
      },
    });
  } catch (error) {
    console.error('Failed to claim referral:', error);
  }
}

function getReferralDisplayText(referral: UserReferral): string {
  const isGuest = !referral.referee?.mobile_number;
  const type = isGuest ? t('REFERRAL_GUEST') : t('REFERRAL_HUNTER');
  return `${type} #${referral.referee?.hunter_id}`;
}

function openQuestionMark(): void {
  track({
    id: 'referral',
    action: 'click',
    data: {
      target: 'question_mark',
    },
  });
  openDialog('referral_question_mark');
}

function goToDialog(dialog: string): void {
  track({
    id: 'referral',
    action: 'click',
    data: {
      target: dialog,
    },
  });
  openDialog(dialog);
}
</script>

<template>
  <div class="fit referral" :class="`season-${seasonCode.toLowerCase()}`">
    <div class="fixed top-0 left-0 flex justify-center items-center w-full h-20 z-50">
      <Button
        track-id="disable-track"
        class="absolute top-2 left-2"
        shape="square"
        variant="secondary"
        @click="push(-1)"
      >
        <Icon name="arrow-left" />
      </Button>
      <div class="text-lg font-bold text-border" v-html="t('REFERRAL_TITLE')" />
      <div class="absolute top-3 right-3">
        <HeaderCrystal />
      </div>
    </div>

    <CarAnimation assets="/blocker/new_car_silver" class="top-[20vw] w-full" />

    <div class="content-container">
      <div class="content-header">
        <div
          class="text-2xl font-bold mb-2"
          v-html="t('REFERRAL_DESC_1', { REWARD: gameSettings?.referral_reward })"
        />
        <div class="text-base mb-5" v-html="t('REFERRAL_DESC_2')" />
      </div>

      <template v-if="isUserLogged">
        <div class="text-sm text-center mb-2">{{ t('REFERRAL_DESC_3') }}</div>

        <div class="referral-link-container" @click="handleCopyReferralLink">
          <div class="text-sm">{{ displayUrl }}</div>
          <Icon name="copy" />
        </div>

        <div class="social-container">
          <a
            v-for="{ icon, link, sharelink } in socialNetworks"
            :key="icon"
            :href="sharelink || link"
            target="_blank"
            rel="noopener noreferrer"
            class="social-link"
          >
            <Icon :name="icon" :size="30" />
          </a>
        </div>

        <div class="referrals-container">
          <div class="referrals-header">
            <div class="font-bold" v-html="t('REFERRAL_HUNTER_ID')" />
            <div class="font-bold" v-html="t('REFERRAL_STATUS')" />
          </div>

          <template v-if="isFetching">
            <div class="flex flex-col gap-2">
              <div v-for="index in SKELETON_ITEMS" :key="index" class="skeleton-item">
                <div class="skeleton-text skeleton-text--short" />
                <div class="skeleton-button" />
              </div>
            </div>
          </template>

          <template v-else-if="!isFetching && referrals.length">
            <div class="flex flex-col gap-2">
              <div v-for="referral in referrals" :key="referral.id" class="referral-item">
                <div class="text-sm">{{ getReferralDisplayText(referral) }}</div>
                <Button
                  track-id="disable-track"
                  v-if="referral.status === 'pending'"
                  size="small"
                  variant="purple"
                  @click="openQuestionMark"
                >
                  <div class="flex items-center gap-2">
                    <div class="text-sm" v-html="t('REFERRAL_BUTTON_PENDING')"></div>
                    <Icon name="question-mark-white" />
                  </div>
                </Button>
                <Button
                  track-id="disable-track"
                  v-else-if="referral.status === 'verified'"
                  size="small"
                  variant="secondary"
                  :loading="isClaimLoading"
                  @click="handleClaimReferral(referral.id)"
                >
                  {{ t('REFERRAL_BUTTON_CLAIM') }}
                </Button>
                <Button track-id="disable-track" size="small" variant="secondary" disable v-else>
                  {{ t('REFERRAL_BUTTON_CLAIMED') }}
                </Button>
              </div>
            </div>
          </template>

          <template v-else>
            <div class="text-sm py-3" v-html="t('REFERRAL_EMPTY_LIST')" />
          </template>
        </div>
      </template>

      <template v-else>
        <Button
          track-id="disable-track"
          class="!mb-5"
          :label="t('REFERRAL_BUTTON_SIGN_UP')"
          @click="goToDialog('sign_up')"
        />
        <div
          class="text-sm underline text-link"
          v-html="t('REFERRAL_DESC_5')"
          @click="goToDialog('login')"
        />
      </template>
    </div>
  </div>
</template>

<style scoped lang="scss">
.referral {
  display: flex;
  flex-flow: column;
  padding: 100% 0 20px;
  background-color: #0f132a !important;

  &.season-sg {
    background:
      linear-gradient(180deg, rgba(15, 19, 42, 0) 0%, #0f132a 14.18%), url('/imgs/kv/sg.png');
    background-repeat: no-repeat;
    background-position:
      center 80vw,
      center -10vw;
    background-size:
      100% 150vw,
      100% 112vw;
  }
  &.season-vn {
    background-size: 100% 112vw !important;
    background: url('/imgs/kv/vn.png') center -10vw no-repeat;
  }

  .text-border {
    text-shadow:
      rgb(65, 6, 60) 2px 0px 0px,
      rgb(65, 6, 60) 1.75517px 0.958851px 0px,
      rgb(65, 6, 60) 1.0806px 1.68294px 0px,
      rgb(65, 6, 60) 0.141474px 1.99499px 0px,
      rgb(65, 6, 60) -0.832294px 1.81859px 0px,
      rgb(65, 6, 60) -1.60229px 1.19694px 0px,
      rgb(65, 6, 60) -1.97998px 0.28224px 0px,
      rgb(65, 6, 60) -1.87291px -0.701566px 0px,
      rgb(65, 6, 60) -1.30729px -1.5136px 0px,
      rgb(65, 6, 60) -0.421592px -1.95506px 0px,
      rgb(65, 6, 60) 0.567324px -1.91785px 0px,
      rgb(65, 6, 60) 1.41734px -1.41108px 0px,
      rgb(65, 6, 60) 1.92034px -0.558831px 0px;
  }
}

.content-container {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: center;
  width: 100%;
  z-index: 9999;
  background-color: #0f132a;
  padding: 20px;
}

.content-header {
  margin-bottom: 20px;
}

.referral-link-container {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  padding: 16px;
  background-color: #2a3f84;
  margin-bottom: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #3a4f94;
  }
}

.social-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.social-link {
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.referrals-container {
  background-color: #04081d;
  padding: 8px 16px 16px;
  border-radius: 8px;
}

.referrals-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  margin-bottom: 8px;
  border-bottom: 1px solid #ffffff;
}

.referral-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.skeleton-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.skeleton-text {
  background: linear-gradient(90deg, #2a3f84 25%, #3a4f94 50%, #2a3f84 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  height: 32px;

  &--short {
    width: 120px;
  }
}

.skeleton-button {
  background: linear-gradient(90deg, #2a3f84 25%, #3a4f94 50%, #2a3f84 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  height: 32px;
  width: 80px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
