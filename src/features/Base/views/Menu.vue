<script lang="ts" setup>
import { useUserStore } from '@stores';
import { getSocials, playSFX } from '@utils';
import { BrandSponsors } from '@enums';
import { useWindowSize } from '@vueuse/core';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { Pagination } from 'swiper/modules';
import { useFetchQueries, useGlobalData, usePageTracker } from '@composables';
import 'swiper/css';
import 'swiper/css/pagination';
import { Swiper as SwiperType } from 'swiper/types';

interface MenuItem {
  title: string;
  icon: string;
  cb: () => void;
  disabled?: boolean;
}

interface SlideMenu {
  title: string;
  content: string;
  cb: () => void;
  disabled?: boolean;
}

const storeUser = useUserStore();
const { userQuery } = useFetchQueries();
const { gameFeatures } = storeToRefs(storeUser);
const { seasonCode, deploymentNumber } = useGlobalData();
const { push, openDialog } = useMicroRoute();
const { t } = useI18n();
const { width } = useWindowSize();
const { track } = usePageTracker();

const pagination = { clickable: true, dynamicBullets: true };
const modules = [Pagination];
const mySwiper = ref<SwiperType>();

function onSwiper(swiper: SwiperType): void {
  mySwiper.value = swiper;
}

const slidesMenu = computed<SlideMenu[]>(() => [
  {
    title: t('MENU_TIP_TRICK_HEADER'),
    content: t('MENU_TIP_TRICK_DESCRIPTION'),
    cb: () => goToDialog('tips_trick'),
  },
  {
    title: t('MENU_FAIRNESS_HEADER'),
    content: t('MENU_FAIRNESS_DESCRIPTION'),
    cb: () => goToPage('ensuring_fairness'),
  },
]);

const doubleMenu = computed(() => [...slidesMenu.value, ...slidesMenu.value]);

const menuItems = computed<MenuItem[]>(() => [
  {
    title: t('MENU_INVENTORY_SQKII_VOUCHERS'),
    icon: '/menu/sqkii-vouchers',
    cb: () => goToPage('sqkii_vouchers'),
    disabled: !gameFeatures.value?.sqkii_voucher,
  },
  {
    title: t('MENU_INVENTORY_FOUND_COIND'),
    icon: '/menu/coin',
    cb: () => goToDialog('enter_serial_number'),
    disabled: !gameFeatures.value?.enter_serial_number,
  },
  {
    title: t('MENU_INVENTORY_REFERRAL'),
    icon: '/menu/referral',
    cb: () => goToPage('referral'),
    disabled: !gameFeatures.value?.referral,
  },
  {
    title: t('MENU_INVENTORY_FAQS'),
    icon: '/menu/questions',
    cb: () => goToPage('faq'),
    disabled: false,
  },
]);

function goToDialog(dialog: string): void {
  track({
    id: 'menu',
    action: 'click',
    data: {
      target: dialog,
    },
  });
  openDialog(dialog);
}

function goToPage(page: string): void {
  track({
    id: 'menu',
    action: 'click',
    data: {
      target: page,
    },
  });
  push(page);
}

function handleBack(): void {
  push(-1);
}

function handleMenuItem(menu: MenuItem): void {
  playSFX('button');
  menu.cb();
}

function handleSlideItem(slide: SlideMenu): void {
  playSFX('button');
  slide.cb();
}

function nextSlide(): void {
  playSFX('button');
  mySwiper.value?.slideNext();
}

function prevSlide(): void {
  playSFX('button');
  mySwiper.value?.slidePrev();
}

onMounted(async () => {
  await nextTick();
  await userQuery.refetch();
});
</script>

<template>
  <div class="pb-5 fullscreen menu" :class="`menu-${seasonCode.toLowerCase()}`">
    <TestingComponent>
      <div class="fixed z-50 text-center text-xs text-[#777] right-0 bottom-2.5 w-full">
        Build ({{ deploymentNumber }}):
        {{ new Date(Number(deploymentNumber) * 1000).toLocaleString() }}
      </div>
    </TestingComponent>
    <div class="flex items-center justify-between px-4 py-5">
      <Button
        track-id="disable-track"
        shape="square"
        variant="secondary"
        @click="goToPage('settings')"
      >
        <Icon name="settings" />
      </Button>
      <Icon :name="`/sov/menu/${BrandSponsors.Sqkii}`" :size="210" class="mt-2" />
      <Button track-id="disable-track" shape="square" @click="handleBack">
        <Icon name="cross" :size="15" />
      </Button>
    </div>

    <div class="w-full h-full overflow-y-auto">
      <!-- <div class="flex items-center justify-center mb-5">
        <div class="relative get-crytals">
          <div
            class="absolute text-lg font-bold text-center -translate-x-1/2 left-1/2 top-7"
            v-html="t('MENU_CARD_GET_CRYSTALS')"
          ></div>
        </div>
        <div class="relative get-hints pointer-events-none opacity-50">
          <div
            class="absolute text-lg font-bold text-center -translate-x-1/2 left-1/2 top-7"
            v-html="t('MENU_CARD_GET_HINTS')"
          ></div>
        </div>
      </div> -->
      <div class="flex items-center justify-center mb-5">
        <div
          v-if="gameFeatures?.brand_action"
          class="relative get-crytals-horizontal"
          @click="goToPage('offer_wall')"
        >
          <div
            class="absolute text-lg font-bold text-center left-[45%] top-1/2 -translate-y-1/2"
            v-html="t('MENU_CARD_GET_CRYSTALS')"
          ></div>
        </div>
      </div>
      <div class="grid items-center justify-center grid-cols-2 px-3 mb-5 gap-x-3 gap-y-4">
        <div
          class="relative flex items-center justify-center gap-1 event"
          :class="{ 'pointer-events-none opacity-50': item.disabled }"
          v-for="(item, index) in menuItems"
          :key="index"
          @click="handleMenuItem(item)"
        >
          <Icon :name="item.icon.replace(/^\//, '')" :size="41" class="absolute top-0 left-0" />
          <div class="flex justify-center w-full font-bold text-center flex-center">
            <div v-html="item.title" class="mr-[-8%]"></div>
          </div>
        </div>
      </div>
      <Swiper
        :slides-per-view="width / 315"
        :initial-slide="1"
        :pagination="pagination"
        :modules="modules"
        :space-between="0"
        centered-slides
        loop
        class="pb-10 mb-8 slide-menu"
        @swiper="onSwiper"
      >
        <SwiperSlide v-for="(s, index) in doubleMenu" :key="index" @click="handleSlideItem(s)">
          <div class="slide-item">
            <div class="p-4 h-[calc(100%-40px)] flex flex-center">
              <div class="flex flex-col justify-center h-full flex-nowrap">
                <div class="text-lg font-bold" v-html="s.title"></div>
                <div class="text-sm" v-html="s.content"></div>
              </div>
            </div>
          </div>
        </SwiperSlide>
        <div class="flex items-center justify-between w-full px-5">
          <div
            @click="prevSlide"
            class="w-[64px] h-[46px] bg-contain"
            :style="{ backgroundImage: `url('/imgs/button/menu_arrow_btn.png')` }"
          ></div>
          <div
            @click="nextSlide"
            class="w-[64px] h-[46px] bg-contain !rotate-180"
            :style="{ backgroundImage: `url('/imgs/button/menu_arrow_btn.png')` }"
          ></div>
        </div>
      </Swiper>

      <div class="mb-5 text-center">
        <div class="mb-5 text-sm" v-html="t('MENU_CTA')"></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
      <div
        @click="goToDialog('adventure_logs')"
        class="mb-5 text-sm text-center underline text-link"
        v-html="t('MENU_PAST_HUNT')"
      ></div>
      <div class="px-5 text-sm text-center opacity-70" v-html="t('MENU_SQKII')"></div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.menu {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  height: 100%;
  overflow: hidden;
  &.menu-vn {
    background: url('/imgs/bg-menu-vn.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  &.menu-sg {
    background: url('/imgs/bg-menu-sg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .get-crytals {
    width: 40vw;
    height: 50vw;
    background-image: url('/imgs/menu/get-crytals.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .get-crytals-horizontal {
    width: 60vw;
    height: 32vw;
    background-image: url('/imgs/menu/get-crytals-horizontal.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .get-hints {
    width: 40vw;
    height: 50vw;
    background-image: url('/imgs/menu/get-hints.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .event {
    max-width: 180px;
    width: 100%;
    height: 47px;
    background-image: url('/imgs/menu/menu_btn.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
}
</style>
<style lang="scss">
.slide-menu {
  .slide-item {
    height: 125px;
    background-image: url('/imgs/menu_slide_item.png');
    background-size: 100% 100%;
    padding: 0 20px;
    background-repeat: no-repeat;
  }
  .swiper-wrapper {
    padding-bottom: 5px;
  }
  .swiper-pagination {
    bottom: 16px;
    display: flex;
    align-items: center;
    width: 65px !important;
    overflow: visible;
  }
  .swiper-pagination-bullet-active {
    width: 15px !important;
    height: 7px !important;
    border-radius: 2px;
    background: #00e0ff !important;
    opacity: 1;
  }
  .swiper-pagination-bullet {
    width: 5px;
    height: 5px;
    background: #425e9c;
    border-radius: 5px !important;
    opacity: 1;
  }
}
</style>
