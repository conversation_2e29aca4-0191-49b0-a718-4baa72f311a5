import { UnifyInstructor } from '@types';

type InstructorType = 'timii' | 'nancii' | 'sqkii' | 'shinobii' | 'rmi';

interface DialogState {
  showInstructor: InstructorType | '';
  instructorData: UnifyInstructor;
}

const createInitialInstructorData = (): UnifyInstructor => ({
  agent: '',
  tag: '',
  sequences: [],
  hiddenAnims: false,
  bubbleAction: true,
  bubbleText: '',
});

export const useDialogStore = defineStore('dialogs', {
  state: (): DialogState => ({
    showInstructor: '',
    instructorData: createInitialInstructorData(),
  }),

  getters: {
    isInstructorOpen: (state) => Boolean(state.showInstructor),
    currentInstructorType: (state) => state.showInstructor,
  },

  actions: {
    openUnifyInstructor(type: InstructorType, params: UnifyInstructor) {
      if (this.showInstructor) {
        this.closeUnifyInstructor();
      }

      this.showInstructor = type;
      this.instructorData = { ...this.instructorData, ...params };
    },

    closeUnifyInstructor() {
      this.showInstructor = '';
      this.resetInstructorData();
    },

    resetInstructorData() {
      this.instructorData = createInitialInstructorData();
    },
  },
});
