<script lang="ts" setup>
import { useDialogStore } from '@stores';

const storeDialog = useDialogStore();

const { showInstructor, instructorData } = storeToRefs(storeDialog);
</script>
<template>
  <TimiiInstructor
    v-if="showInstructor === 'timii'"
    :agent="instructorData.agent"
    :sequences="instructorData.sequences"
    :hidden-anims="instructorData.hiddenAnims"
  />
  <SqkiiInstructor
    v-if="showInstructor === 'sqkii'"
    :bubble-text="instructorData.bubbleText"
    :agent="instructorData.agent"
    :sequences="instructorData.sequences"
    :hidden-anims="instructorData.hiddenAnims"
    :bubble-action="instructorData.bubbleAction"
  />
  <NanciiInstructor
    v-if="showInstructor === 'nancii'"
    :agent="instructorData.agent"
    :sequences="instructorData.sequences"
    :tag="instructorData.tag"
  />
  <ShinobiiInstructor
    v-if="showInstructor === 'shinobii'"
    :agent="instructorData.agent"
    :sequences="instructorData.sequences"
    :tag="instructorData.tag"
  />
  <RMISqkiiInstructor
    v-if="showInstructor === 'rmi'"
    :agent="instructorData.agent"
    :sequences="instructorData.sequences"
    :bubble-text="instructorData.bubbleText"
  />
</template>
