<script setup lang="ts">
import { useGlobalInstructor, usePageTracker } from '@composables';
import { BrandSponsors } from '@enums';
import { UnifyInstructor, ActionCallback } from '@types';
import gsap, { Power3, Linear } from 'gsap';
import TextPlugin from 'gsap/TextPlugin';
import CSSRulePlugin from 'gsap/CSSRulePlugin';

gsap.registerPlugin(TextPlugin, CSSRulePlugin);

interface Emits {
  (e: 'close'): void;
}

const props = withDefaults(defineProps<UnifyInstructor>(), {
  agent: '',
  tag: '',
});

const emits = defineEmits<Emits>();

const { closeUnifyInstructor: closeUnifyInstructorGlobal } = useGlobalInstructor();
const { tracker } = usePageTracker();

const tl = gsap.timeline();

const currentSequenceIndex = ref(0);
const isActionInProgress = ref(false);

const agentCharacter = computed(() => {
  return props.agent || `/sov/character/${BrandSponsors.Sqkii}_nancii`;
});

const currentSequence = computed(() => props.sequences[currentSequenceIndex.value]);
const hasMessage = computed(() => Boolean(currentSequence.value?.message));
const isLastSequence = computed(() => currentSequenceIndex.value >= props.sequences.length - 1);
const hasTag = computed(() => Boolean(props.tag));

const backdropStyle = computed(() => {
  const css = currentSequence.value?.backdropCss;
  return typeof css === 'string' ? css : undefined;
});

const instructorStyle = computed(() => {
  return currentSequence.value?.css || {};
});

const SELECTORS = {
  TEXT: '#text',
  INSTRUCTOR: '.instructor',
  BACKDROP: '.i_backdrop',
  AGENT: '.agent',
  BODY: '.body',
  TAG: '.tag',
  CROSS: '.cross',
  BUBBLE_NAME: '.bubble-name',
  ACTION: '.action',
} as const;

async function handleAction(action?: ActionCallback): Promise<void> {
  if (isActionInProgress.value) return;

  isActionInProgress.value = true;

  try {
    if (!currentSequence.value) return;
    if (currentSequence.value.persistent) return;

    if (!action) {
      await goToNext();
      return;
    }

    await action(goToNext, closeUnifyInstructor);
  } finally {
    isActionInProgress.value = false;
  }
}

async function goToNext(targetIndex?: number): Promise<void> {
  if (targetIndex !== undefined && targetIndex < props.sequences.length) {
    currentSequenceIndex.value = targetIndex;
    return;
  }

  if (!isLastSequence.value) {
    currentSequenceIndex.value += 1;
    return;
  }

  await closeUnifyInstructor();
}

async function closeUnifyInstructor(): Promise<void> {
  await hideWithTransition();
  closeUnifyInstructorGlobal();
  tracker({
    id: 'nancii_instructor',
    action: 'click',
    data: {
      target: 'tap',
      message: currentSequence.value?.message || '',
    },
  });
  emits('close');

  isActionInProgress.value = false;
}

async function animateText(): Promise<void> {
  if (!hasMessage.value || !currentSequence.value?.message) return;

  const textElement = SELECTORS.TEXT;

  gsap.set(textElement, { pointerEvents: 'none' });

  try {
    await gsap.fromTo(
      textElement,
      { text: '' },
      {
        delay: currentSequenceIndex.value ? 0 : 1,
        text: currentSequence.value.message,
        duration: 1,
        ease: Linear.easeNone,
      },
    );
  } finally {
    gsap.set(textElement, { pointerEvents: 'all' });
  }
}

// Watch for sequence changes and trigger text animation
watchEffect(() => animateText(), { flush: 'post' });

async function showWithTransition(): Promise<void> {
  const instructorElement = SELECTORS.INSTRUCTOR;

  gsap.set(instructorElement, { pointerEvents: 'none' });

  try {
    if (!hasMessage.value) return;

    await tl
      .fromTo(
        SELECTORS.AGENT,
        { y: 12, xPercent: 1, opacity: 0 },
        { y: 0, xPercent: 2, opacity: 1, duration: 0.8 },
      )
      .fromTo(SELECTORS.BODY, { opacity: 0, width: 0 }, { opacity: 1, width: '100%', duration: 1 })
      .fromTo(SELECTORS.TAG, { opacity: 0, x: 10 }, { opacity: 1, x: 0, duration: 0.5 })
      .fromTo(SELECTORS.CROSS, { opacity: 0, x: -5 }, { opacity: 1, x: 0 });
  } finally {
    gsap.set(instructorElement, { pointerEvents: 'all' });
  }
}

async function hideWithTransition(): Promise<void> {
  const elements = [SELECTORS.INSTRUCTOR, SELECTORS.BACKDROP];

  gsap.set(elements, { pointerEvents: 'none' });

  try {
    if (!hasMessage.value) return;

    await tl
      .to(SELECTORS.BODY, {
        y: -5,
        opacity: 0,
        duration: 0.5,
        ease: Power3.easeIn,
      })
      .to(
        [SELECTORS.AGENT, SELECTORS.BUBBLE_NAME, SELECTORS.BACKDROP],
        { opacity: 0, y: 10 },
        '-=0.5',
      );
  } finally {
    gsap.set(elements, { pointerEvents: 'all' });
  }
}

onMounted(async () => {
  await nextTick();
  await showWithTransition();
});

onBeforeUnmount(() => {
  const elementsToCleanup = [
    SELECTORS.ACTION,
    SELECTORS.TEXT,
    SELECTORS.INSTRUCTOR,
    SELECTORS.BACKDROP,
  ];

  elementsToCleanup.forEach((selector) => {
    gsap.killTweensOf(selector);
  });

  tl.kill();
});
</script>

<template>
  <Teleport :to="currentSequence?.target || 'body'">
    <!-- Animated backdrop -->
    <Transition
      appear
      enter-active-class="animated fadeIn"
      leave-active-class="animated fadeOut"
      :duration="300"
    >
      <div
        v-if="currentSequence?.backdropCss"
        class="i_backdrop"
        :style="backdropStyle || undefined"
        @click="handleAction(currentSequence?.actions?.cb)"
      />
    </Transition>

    <!-- Main instructor container -->
    <div class="instructor" :style="instructorStyle">
      <div v-if="hasMessage" class="wrapper" @click="handleAction(currentSequence?.actions?.cb)">
        <!-- Agent avatar -->
        <div class="agent">
          <Icon :name="agentCharacter" class="w-full h-full object-contain" />
        </div>

        <!-- Message body -->
        <div class="body">
          <div id="text" v-html="currentSequence?.message" />
        </div>

        <!-- Tag/decoration -->
        <div v-if="hasTag" class="tag absolute right-0 -bottom-1">
          <Icon :name="tag" :size="150" />
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style lang="scss" scoped>
.i_backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 7000;
}
.instructor {
  z-index: 10001;
  position: fixed;
  left: 0;
  bottom: 40px;
  right: 0;
  transition: bottom 0.4s ease-in-out;

  .wrapper {
    position: relative;
    display: flex;
    width: 100%;
  }
  .agent {
    position: absolute;
    left: -24px;
    bottom: 10px;
    width: 138px;
    z-index: 2;
  }
  .body {
    position: relative;
    display: flex;
    align-items: center;
    height: max-content;
    min-height: 132px;
    width: 100%;
    padding: 25px 15px 50px 110px;
    background-size: 100% 100%;
    background-image: url('/imgs/speech-box.png');
    background-repeat: no-repeat;
    #text {
      margin-left: 10px;

      padding: 10px 0 5px;
    }
  }
}
</style>
