import { useGlobalStore, useMapStore } from '@stores';
import {
  GeolocateSuccess,
  LngLatBoundsLike,
  Map,
  MapCreationStatus,
  GeolocateControl,
  MapOptions,
  useMapbox,
} from 'vue3-maplibre-gl';
import RiveLayer from '@plugins/MapPlugins/libs/deckgl/RiveLayer';
import { MapboxOverlay } from '@deck.gl/mapbox';

export function useGlobalMap() {
  const MAP_OPTIONS: MapOptions = {
    container: 'map',
    style: process.env.APP_MAP_STYLE as string,
    minZoom: 9,
    maxZoom: 19,
    zoom: 9.4,
    center: [103.8097, 1.3535],
    // preserveDrawingBuffer: true,
  };

  const GEOLOCATE_OPTIONS = {
    positionOptions: {
      enableHighAccuracy: true,
    },
    trackUserLocation: true,
    showAccuracyCircle: true,
  };

  const storeGlobal = useGlobalStore();
  const storeMap = useMapStore();

  const { register: registerMap, mapInstance, mapStatus } = useMapbox();

  const isMapLoaded = computed(() => {
    return mapStatus.value === MapCreationStatus.Loaded;
  });

  async function loadMap(map: Map): Promise<void> {
    if (!map) return;
    map.touchZoomRotate.disableRotation();
    map.doubleClickZoom.disable();
    map.dragRotate.disable();
    map.touchPitch.disable();

    map.preserveDrawingBuffer = true
    const bounds: LngLatBoundsLike = [90, -10, 150, 25];
    map.setMaxBounds(bounds);
    const atlas = await (await fetch('badges.json')).json();
    const layer = new RiveLayer({
      id: 'rive-layer',
      atlas: atlas,
      sizeScale: 10,
      zoomBasedSizing: true, // Enable zoom-based sizing
      zoomStops: [
        { zoom: 9, scale: 0.5 }, // Small icons at low zoom
        { zoom: 11, scale: 0.6 }, // Medium icons
        { zoom: 13, scale: 0.8 }, // Normal size at medium zoom
        { zoom: 15, scale: 1 }, // Larger icons at high zoom
        { zoom: 18, scale: 1.5 }, // Largest icons at max zoom
      ],
      pickable: true,
      onClick: function ({ object, layer }) {
        object.icon = 'github';
        layer?.getAttributeManager()?.invalidateAll();
        layer?.setNeedsUpdate();
      },
      data: [
        {
          x: 103.8097,
          y: 1.3535,
          icon: 'rate',
          size: 5,
        },
        {
          x: 103.8097,
          y: 1.3635,
          icon: 'support',
          size: 5,
        },
        {
          x: 103.809,
          y: 1.3635,
          icon: 'discount',
          size: 5,
        },
        {
          x: 103.8095,
          y: 1.3635,
          icon: 'github',
          size: 5,
        },
      ],
      getPosition: (d) => [d.x, d.y],
      getIcon: (d) => d.icon,
      getSize: (d) => d.size,
    });
    const overlay = new MapboxOverlay({
      interleaved: false,
      layers: [layer],
    });

    map.addControl(overlay);

  }

  function registerGeoInstance(g: GeolocateControl): void {
    storeMap.setGeoInstance(g);
  }

  function setGeoInstance(tt: GeolocateSuccess): void {
    const { _watchState } = tt.target;
    storeMap.setGeoState(_watchState);
  }

  function handleErrorGPS(): void {
    storeMap.setGeoState('UNAVAILABLE');
  }

  function handleSuccessGPS(tt: GeolocateSuccess): void {
    const { coords } = tt;
    storeMap.setGeoState(tt.target._watchState);
    storeGlobal.setUserLocation([coords.longitude, coords.latitude]);
  }

  watch(isMapLoaded, async (loaded) => {
    if (loaded && mapInstance.value) {
      storeMap.setMapInstance(mapInstance.value);
      storeMap.setIsMapLoaded(true);
      await loadMap(mapInstance.value);
    }
  });

  return {
    MAP_OPTIONS,
    GEOLOCATE_OPTIONS,
    isMapLoaded,
    loadMap,
    registerMap,
    registerGeoInstance,
    setGeoInstance,
    handleErrorGPS,
    handleSuccessGPS,
  };
}
