<script lang="ts" setup>
import { dateTimeFormat, FULL_DATE_MONTH_TIME_24H_FORMAT, numeralFormat } from '@utils';
import { useSVStore } from '@stores';
import { SVPaymentResult } from '@types';

type PaymentStatus = 'success' | 'failed' | 'processing';

interface Props {
  status: PaymentStatus;
  data?: SVPaymentResult;
}

interface StatusConfig {
  readonly header: string;
  readonly icon?: string;
  readonly iconSize: number;
}

const PROCESSING_TIMEOUT = 5;
const DEFAULT_CURRENCY = 'S$';

const STATUS_CONFIGS: Record<PaymentStatus, StatusConfig> = {
  processing: {
    header: 'PAYMENT_RESULT_HEADER_1',
    iconSize: 0,
  },
  success: {
    header: 'PAYMENT_RESULT_HEADER_2',
    icon: 'top-up-success',
    iconSize: 140,
  },
  failed: {
    header: 'PAYMENT_RESULT_HEADER_3',
    icon: 'top-up-failed',
    iconSize: 140,
  },
} as const;

const props = defineProps<Props>();

const storeSV = useSVStore();

const { svUser } = storeToRefs(storeSV);
const { t } = useI18n();
const { closeAllDialogs, push } = useMicroRoute();

const currentStatus = ref<PaymentStatus>(props.status);
const timeRemaining = ref(PROCESSING_TIMEOUT);
const processingInterval = ref<NodeJS.Timeout | null>(null);

const statusConfig = computed(() => STATUS_CONFIGS[currentStatus.value]);
const headerText = computed(() => t(statusConfig.value.header));

const userCurrency = computed(() => svUser.value?.currency || DEFAULT_CURRENCY);

const formattedAmount = computed(() => {
  if (!props.data?.amount) return '';
  return numeralFormat(Number(props.data.amount), '0,0.00');
});

const formattedDateTime = computed(() => {
  if (!props.data?.completed_at) return '';
  return dateTimeFormat(props.data.completed_at, FULL_DATE_MONTH_TIME_24H_FORMAT);
});

const isProcessing = computed(() => currentStatus.value === 'processing');
const isSuccess = computed(() => currentStatus.value === 'success' && !!props.data);

const hasPaymentData = computed(() => Boolean(props.data));

function determineResultStatus(): PaymentStatus {
  return hasPaymentData.value ? 'success' : 'failed';
}

function startProcessingTimer(): void {
  if (currentStatus.value !== 'processing') return;

  processingInterval.value = setInterval(() => {
    timeRemaining.value -= 1;

    if (timeRemaining.value <= 0) {
      stopProcessingTimer();
      currentStatus.value = determineResultStatus();
    }
  }, 1000);
}

function stopProcessingTimer(): void {
  if (processingInterval.value) {
    clearInterval(processingInterval.value);
    processingInterval.value = null;
  }
}

function handleTryAgain(): void {
  push(-1);
  closeAllDialogs();
}

onMounted(async () => {
  await nextTick();
  startProcessingTimer();
});

onBeforeUnmount(() => {
  stopProcessingTimer();
});
</script>
<template>
  <Dialog hide-close>
    <template #header>
      <div v-html="headerText" />
    </template>

    <!-- Processing State -->
    <template v-if="isProcessing">
      <Icon class="!w-full rounded-lg mb-5" name="sqkii_voucher_kv" />
      <div class="text-base text-center" v-html="t('PAYMENT_RESULT_DESC_1')" />
    </template>

    <!-- Success State -->
    <template v-else-if="isSuccess">
      <div class="silver-coin">
        <Icon class="mt-10" :name="statusConfig.icon!" :size="statusConfig.iconSize" />
      </div>

      <div class="banner mb-5">
        <div class="text-3xl font-bold">{{ userCurrency }} {{ formattedAmount }}</div>
        <div class="text-sm" v-html="t('PAYMENT_RESULT_VOUCHERS_USED')" />
        <div class="text-balance font-bold" v-html="data!.outlet_name" />
      </div>

      <div class="text-center">
        <div class="text-lg font-bold text-[#00E0FF]">
          {{ formattedDateTime }}
        </div>

        <div
          class="text-base opacity-50 mb-8"
          v-html="t('PAYMENT_RESULT_DESC_2', { ID: data!.payment_id })"
        />

        <div class="flex flex-nowrap items-start gap-2 mb-5">
          <Icon name="alert-circle" class="mt-2 flex-shrink-0" />
          <div class="text-sm text-left" v-html="t('PAYMENT_RESULT_DESC_3')" />
        </div>

        <Button
          track-id="disable-track"
          :label="t('PAYMENT_RESULT_BTN_1')"
          class="w-full"
          @click="closeAllDialogs"
        />
      </div>
    </template>

    <!-- Failed State -->
    <template v-else>
      <div class="silver-coin">
        <Icon class="mt-10" :name="statusConfig.icon!" :size="statusConfig.iconSize" />
      </div>

      <div class="text-sm text-center mb-5 px-5 -mt-10" v-html="t('PAYMENT_RESULT_DESC_4')" />

      <div class="flex items-center flex-nowrap gap-5">
        <Button
          track-id="disable-track"
          :label="t('PAYMENT_RESULT_BTN_2')"
          variant="purple"
          size="max-content"
          class="flex-1"
          @click="handleTryAgain"
        />

        <Button
          track-id="disable-track"
          :label="t('PAYMENT_RESULT_BTN_3')"
          size="max-content"
          class="flex-1"
          @click="closeAllDialogs"
        />
      </div>
    </template>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/big-glow-2.png');
  background-size: cover;
  background-position: center;
  margin-left: -30px;
  margin-top: -50px;
}

.banner {
  margin-top: -100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 2px;
  width: 76vw;
  height: 33vw;
  background-image: url('/imgs/shrink-circle-banner.png');
  background-size: 100% 100%;
  padding: 10px;
}
</style>
